// 生产计划总览
import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-plan-overview-v1`

// 获取总览详情
export function getPlanOverviewDetails(params) {
	return request({
		url: `${baseUrl}/getPlanOverviewDetail`,
		method: 'get',
		params,
	})
}
export function getAllList(params) {
	return request({
		url: `${baseUrl}/list`,
		method: 'get',
		params,
	})
}
/** 根据项目编码获取生产基地 */
export function getAddressList(projectCode) {
	return request({
		url: `${baseUrl}/queryAddressListByProjectCode`,
		method: 'get',
		params: { projectCode },
	})
}

// 导出excel
export function exportProcess(params) {
	return request({
		url: `${baseUrl}/excel/export`,
		method: 'post',
		params,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}

// 生产计划总览详情导出Excel数据
export function planOverviewDetail(params) {
	return request({
		url: `${baseUrl}/excel/export/planOverviewDetail`,
		method: 'post',
		params,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}
// 生产计划总览详情导出Excel数据URL
export const excelOfUrl = `${baseUrl}/excel/export/planOverviewDetail`
