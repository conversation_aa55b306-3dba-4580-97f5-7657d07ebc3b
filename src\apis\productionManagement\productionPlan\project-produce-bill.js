import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
export function getList(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/page`,
		method: 'get',
		params,
		isFormatParams: true,
		isFormatResponse: true,
	})
}

/** 获取统计数据 */
export function getCommonTotal(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/commonTotal`,
		method: 'get',
		params,
	})
}
// 生成项目产品
export const createProduce = params => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/build/project/products`,
		method: 'post',
		params,
		timeout: -1,
	})
}
// 生成产品清单
export const createProBill = ({ data, params }) => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/add/batch/material`,
		method: 'post',
		data,
		params,
		timeout: -1,
	})
}

// 批量编辑
export const edits = data => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/update/batch`,
		method: 'put',
		data,
	})
}
export const getCounts = params => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/status/count`,
		method: 'get',
		params,
	})
}
/** 获取生产中 未生产数量 */
export const getPageCount = params => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/pageCount`,
		method: 'get',
		params,
	})
}
//新增接口
export function add(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/add`,
		method: 'post',
		data,
		isFormatParams: true,
		isFormatResponse: true,
	})
}
//编辑接口
export function edit(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/update`,
		method: 'put',
		data,
		isFormatParams: true,
		isFormatResponse: true,
	})
}
//查询单条详情
export const getDetail = params => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/detail`,
		method: 'get',
		params,
		isFormatParams: true,
		isFormatResponse: true,
	})
}
//批量删除
export function deleteRows(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/logicDelete/batch`,
		method: 'delete',
		params,
	})
}

export const excelOfIdsUrl = `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/excel/exportIds`
export const excelOfFiltersUrl = `/hpcc-ipop/v1_0/module/ipop-aps-product-list-v1/excel/export`

export default {
	getList,
	getDetail,
	add,
	edit,
	deleteRows,
	getCounts,
	excelOfIdsUrl,
	excelOfFiltersUrl,
}
