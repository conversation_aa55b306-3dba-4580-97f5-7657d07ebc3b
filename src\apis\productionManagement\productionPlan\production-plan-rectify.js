// 生产计划编制
import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-plan-redress-v1`
const plan = `/hpcc-ipop/v1_0/module/ipop-plan-project-month-v1`
// 获取分页列表
export function getPageList(params) {
	return request({
		url: `${baseUrl}/page`,
		method: 'get',
		params,
	})
}

// 获取生产计划纠编审核状态数量统计接口
export function getPageListCountApi(params) {
	return request({
		url: `${baseUrl}/getPageDataStatusCount`,
		method: 'get',
		params,
	})
}

//批量删除
export function deleteRows(params) {
	return request({
		url: `${baseUrl}/logicDelete/batch`,
		method: 'delete',
		params,
	})
}

/**
 * 获取二级项目情况
 * @param projectSecondCode 二级项目编号
 */
export function getProjectSituation(params) {
	return request({
		url: `${plan}/getProjectSituation`,
		method: 'get',
		params,
	})
}

// 获取生产计划纠偏详情
export function getPlanRedressDetail(params) {
	return request({
		url: `${baseUrl}/getPlanRedressDetail`,
		method: 'get',
		params,
	})
}

// 获取项目月度计划详情
export function getPlanProjectMonthDetail(params) {
	return request({
		url: `${plan}/getPlanProjectMonthDetail`,
		method: 'get',
		params,
	})
}

// 项目月度计划删除

export function yearsDeleteRows(params) {
	return request({
		url: `${plan}/logicDelete/batch`,
		method: 'delete',
		params,
	})
}

// 新增项目月度计划(纠编)

export function addPlanRedress(data) {
	return request({
		url: `${plan}/addPlanRedress`,
		method: 'post',
		data,
	})
}

// 新增
export function addDraft(data) {
	return request({
		url: `${baseUrl}/add`,
		method: 'post',
		data,
	})
}
// 纠偏修改
export function edit(data) {
	return request({
		url: `${baseUrl}/update`,
		method: 'put',
		data
	})
}

// 修改
export function projectMonthedit(data, headers) {
	return request({
		url: `${plan}/update`,
		method: 'put',
		data,
		headers,
	})
}

// 发起流程
export function startProcess(data) {
	return request({
		url: `${baseUrl}/startProcessByCode`,
		method: 'post',
		data,
	})
}

// 导出excel
export function exportProcess(params) {
	return request({
		url: `${baseUrl}/excel/export`,
		method: 'post',
    params,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}
// 导出选择excel
export function exportProcessByIds(data) {
	return request({
		url: `${baseUrl}/excel/exportIds`,
		method: 'post',
		data,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}
