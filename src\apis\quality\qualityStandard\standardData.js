// 质量控制-标准资料
import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-qms-standard-file-v1`

// 获取分页列表
export function getPageListApi(params) {
	return request({
		url: `${baseUrl}/page`,
		method: 'get',
		params,
	})
}

// 获取审核状态数量统计接口
export function getPageListCountApi(params) {
	return request({
		url: `${baseUrl}/getPageDataStatusCount`,
		method: 'get',
		params,
	})
}

// 新增
export function addApi(data) {
	return request({
		url: `${baseUrl}/add`,
		method: 'post',
		data,
	})
}

// 修改
export function editApi(data) {
	return request({
		url: `${baseUrl}/update`,
		method: 'put',
		data,
	})
}

// 详情接口
export function getDetailApi(params) {
	return request({
		url: `${baseUrl}/detail`,
		method: 'get',
		params
	})
}

// 逻辑删除接口, ids以逗号分隔多个id
export function deleteRowsApi(params) {
	return request({
		url: `${baseUrl}/logicDelete/batch`,
		method: 'DELETE',
		params,
	})
}

/** 发起流程 */
export function startProcessApi(data) {
	return request({
		url: `${baseUrl}/startProcessByCode`,
		method: 'post',
		data,
	})
}

export const excelOfIdsUrl = `${baseUrl}/excel/exportIds`
export const excelOfFiltersUrl = `${baseUrl}/excel/export`

