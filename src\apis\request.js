import axios from 'axios'
import { MessageBox, Message, Loading } from 'element-ui'
import store from '@/store'
import { logout } from '@/apis/app/login'
import { getAppUserToken, getRedirectUrl } from '@/utils/request'

// 处理loading状态
let requestUtils = {
	loadingExample: null,
	loadIngCount: 0,
	showLoading() {
		this.loadingExample = Loading.service({
			fullscreen: true,
			lock: true,
			text: '操作中，请稍候...',
			background: 'rgba(0, 0, 0, 0.7)',
		})
	},
	hideLoading() {
		this.loadingExample && this.loadingExample.close()
	},
}
// 创建代理对象
const handler = {
	// 拦截属性读取操作
	get: function (target, prop, receiver) {
		const value = Reflect.get(target, prop, receiver)
		return value
	},
	// 拦截属性写入操作
	set: function (target, prop, value, receiver) {
		if (prop === 'loadIngCount') {
			const oldVal = Reflect.get(target, prop, receiver)
			if (oldVal == 0 && value > 0) {
				target.showLoading()
			}
			if (oldVal > 0 && value == 0) {
				// 延时关闭加载状态，解决接口需要按顺序调用时，存在间隔时间导致的加载状态闪烁
				setTimeout(() => {
					const nowVal = Reflect.get(target, prop, receiver)
					if (nowVal == 0) {
						target.hideLoading()
					}
				}, 50)
			}
		}
		return Reflect.set(target, prop, value, receiver)
	},
}
// 创建监听--监听需要loading状态的请求
const requestUtilsProxy = new Proxy(requestUtils, handler)

// 配置请求头
const getHeaders = (headers = {}) => {
	let header = {
		'X-TenantId': 1,
		'X-appId': process.env.VUE_APP_ID,
		redirectUrl: getRedirectUrl(),
		'x-user-token': getAppUserToken(),
		'x-token': getAppUserToken(),
		'x-mock': 0,
		...headers,
		...(JSON.parse(sessionStorage.getItem('bpm-headers')) || {}),
		'X-auth-data-scope': sessionStorage.getItem('X-auth-data-scope'),
	}
	if (store.getters.userInfo.userNo) {
		header.uid = store.getters.userInfo.userNo
	}
	return header
}
// 响应时将null转化为undefined---测试版
const formatResponse = params => {
	if (params === null) {
		return undefined
	}
	if (typeof params != 'object') {
		return params
	}
	if (Array.isArray(params)) {
		for (let i = 0; i < params.length; i++) {
			const item = params[i]
			params[i] = formatResponse(item)
		}
	} else {
		for (let k in params) {
			if (params[k] === null) {
				params[k] = undefined
				continue
			}
			if (typeof params[k] === 'object') {
				params[k] = formatResponse(params[k])
			}
		}
	}
	return params
}
// 提交时将undefined转化为null---测试版
const formatParams = params => {
	if (params === null) {
		return null
	}
	if (params instanceof FormData) {
		return params
	}
	if (typeof params != 'object') {
		return params
	}
	let res = {}
	if (Array.isArray(params)) {
		res = []
		for (let i = 0; i < params.length; i++) {
			const item = params[i]
			res[i] = formatParams(item)
		}
	} else {
		for (let k in params) {
			if (params[k] === null) continue
			if (typeof params[k] != 'object') {
				res[k] = params[k] === undefined ? null : params[k]
			} else {
				res[k] = formatParams(params[k])
			}
		}
	}
	return res
}
// 创建axios配置
const service = axios.create({
	timeout: 20000,
	headers: {
		'Content-Type': 'application/json;charset=UTF-8',
		Accept: 'application/json',
	},
})
// 跨域通知
let showCORSNotify = false
// 请求拦截器
service.interceptors.request.use(
	config => {
		if (!config.hideLoading) {
			requestUtilsProxy.loadIngCount++
		}
		return config
	},
	error => {
		requestUtilsProxy.loadIngCount++
		return Promise.reject(error)
	}
)
// 响应拦截器

service.interceptors.response.use(
	async response => {
		if (!response.config.hideLoading) {
			requestUtilsProxy.loadIngCount--
		}
		// 网络错误
		if (response.status !== 200) {
			Message({
				message: response.data.message || 'Error',
				type: 'error',
			})
			return Promise.reject(new Error(response.data.errMessage || 'Error'))
		} else {
      showCORSNotify = false
			//20001--需要重定向
			if (20001 === +response.data.errCode) {
				return (window.location.href = response.data.redirect_url)
			}
			// 登录失效
			if (1003 === +response.data.errCode) {
				// 若被iframe嵌套，不处理登录失效问题
				if(window.top!==window) return
				const res = await logout()
				res.data && (window.location.href = res.data)
			}
			// 业务错误
			if (response.data.error || (response.data.hasOwnProperty('success') && !response.data.success)) {
				response.data.errMessage = response.data.errMessage.replaceAll(
					'cn.net.ceec.hpcc.ipop.module.biz.exception.SelfBizRuntimeException: ',
					''
				)
				Message({
					message: response.data.errMessage,
					type: 'error',
				})
				// return Promise.reject(new Error(response.data.errMessage || 'Error'))
        return response
			}
			return response || {}
		}
	},
	error => {
		requestUtilsProxy.loadIngCount--
		let response = error
    // 判断是否跨域，如果跨域一般是零信任掉线，需要给予用户提醒
    if ((!error.response || !error.response.responseURL) && error.code === "ERR_NETWORK") {
      if (showCORSNotify) return
      showCORSNotify = true
      MessageBox({
        title: "提示",
        message: `
          <p style="text-indent: 2em;">访问遇到异常，一般情况下是由于零信任登录过期引起，请尝试如下操作恢复访问：</p>
          <ul style="text-indent: 2em;">
            <li>1、注销并重新登录零信任(<a href="https://i.htpc.com.cn" target="_blank">https://i.htpc.com.cn</a>)。</li>
            <li>2、注销并重新登录湖南火电门户(<a href="https://home.htpc.com.cn" target="_blank">https://home.htpc.com.cn</a>)。</li>
            <li>3、重新登录“E-MIS”系统(<a href="https://ipop.htpc.com.cn" target="_blank">https://ipop.htpc.com.cn</a>)。</li>
          </ul>
        `,
        dangerouslyUseHTMLString: true,
        customClass: "w-[450px]",
        confirmButtonClass: "text-[white] border-[var(--primary-color)]",
      })
    }
		if (response) {
			Message({
				message: error.message || 'Error',
				type: 'error',
			})
		}
		return Promise.reject({
			error: error,
			reason: error.message,
			success: false,
			data: response != undefined ? response.data : '',
		})
	}
)

/**
 * 封装request请求
 * @param url
 * @param method
 * @param data
 * @param headers   ---  自定义请求头
 * @param responseType
 * @param params
 * @param hideLoading  ---  是否隐藏loading动画
 * @param timeout  --- 设置timeout时间
 * @param showResponseHeaders  --- 是否需要读取响应头,文件类接口可能需要读取响应头内容时设置
 * @returns {Promise}
 */

function request({
	url,
	method,
	data = {},
	headers,
	responseType,
	params = {},
	hideLoading,
	timeout,
	showResponseHeaders = false,
	baseURL = process.env.VUE_APP_BASE_API,
	onUploadProgress,
	onError,
	isFormatParams = false, //上传参数是是否将undefined转化为null
	isFormatResponse = false, //下载参数是是否将null转化为undefined
}) {
	// 设置自定义请求头
	;['Content-Type', 'Accept'].forEach(h => {
		headers && headers[h] && (service.defaults.headers[h] = headers[h])
	})
	if (url.match(/getTempFlowNo([^&]+)/) != null) {
		appid = 0
	}
	return new Promise((resolve, reject) => {
		service
			.request({
				url,
				method,
				data: isFormatParams ? formatParams(data) : data,
				// 默认与设置有个优先级
				headers: getHeaders(headers),
				responseType,
				params: params,
				hideLoading,
				timeout,
				baseURL,
				onUploadProgress,
			})
			.then(res => {
				const data = isFormatResponse ? formatResponse(res.data) : res.data
				res = showResponseHeaders ? res : data
				resolve(res)
			})
			.catch(err => {
				onError && onError()
				reject(err)
			})
	})
}

export { request }
