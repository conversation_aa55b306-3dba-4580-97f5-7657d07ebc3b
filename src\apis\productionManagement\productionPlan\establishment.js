// 生产计划编制
import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-plan-redact-v1`
const plan = `/hpcc-ipop/v1_0/module/ipop-plan-project-month-v1`
// 获取分页列表
export function getPageList(params) {
	return request({
		url: `${baseUrl}/page`,
		method: 'get',
		params,
	})
}

// 获取生产计划编制审核状态数量统计接口
export function getPageListCountApi(params) {
	return request({
		url: `${baseUrl}/getPageDataStatusCount`,
		method: 'get',
		params,
	})
}
// 生产计划编制详情导出Excel数据
export function planOverviewDetail(params) {
	return request({
		url: `${baseUrl}/excel/export/planOverviewDetail`,
		method: 'post',
		params,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}
// 获取生产计划编制详情
export function getPlanRedactDetail(params) {
	return request({
		url: `${baseUrl}/getPlanRedactDetail`,
		method: 'get',
		params,
	})
}

/**
 * 获取二级项目列表 弹窗选择分页(二级项目)
 */
export function getprojectSecondPageForPopWindow(params) {
	return request({
		url: `${plan}/projectSecondPageForPopWindow`,
		method: 'get',
		params,
	})
}

/**
 * 获取二级项目情况
 * @param projectSecondCode 二级项目编号
 */
export function getProjectSituation(params) {
	return request({
		url: `${plan}/getProjectSituation`,
		method: 'get',
		params,
	})
}

// 获取不分页
export function getAllList(params) {
	return request({
		url: `${baseUrl}/list`,
		method: 'get',
		params,
	})
}

//批量删除
export function deleteRows(params) {
	return request({
		url: `${baseUrl}/logicDelete/batch`,
		method: 'delete',
		params,
	})
}

// 项目月度计划删除

export function yearsDeleteRows(params) {
	return request({
		url: `${plan}/logicDelete/batch`,
		method: 'delete',
		params,
	})
}

// 保存草稿新增
export function addDraft(data) {
	return request({
		url: `${baseUrl}/add`,
		method: 'post',
		data,
	})
}
// 编辑接口
export function editDraftRedact(data) {
	return request({
		url: `${baseUrl}/update`,
		method: 'put',
		data,
	})
}

// 项目月度计划修改
export function editDraft(data, headers) {
	return request({
		url: `${plan}/update`,
		method: 'put',
		data,
		headers,
	})
}

// 项目月度计划六个月修改
export function sixMonthEdit(data, headers) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-plan-project-month-item-v1//update`,
		method: 'put',
		data,
		headers,
	})
}
// 发起流程
export function startProcess(data) {
	return request({
		url: `${baseUrl}/startProcessByCode`,
		method: 'post',
		data,
	})
}

// 导出excel
export function exportProcess(params) {
	return request({
		url: `${baseUrl}/excel/export`,
		method: 'post',
		params,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}
// 导出选择excel
export function exportProcessByIds(data) {
	return request({
		url: `${baseUrl}/excel/exportIds`,
		method: 'post',
		data,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}

// 项目月度计划新增

export function addPlanRedact(data) {
	return request({
		url: `${plan}/addPlanRedact`,
		method: 'post',
		data,
	})
}

// 获取月度生产计划详情
export function getPlanningRedactDetail(id) {
	return request({
		url: `${plan}/getPlanProjectMonthDetail`,
		method: 'get',
		params: {
			id,
		},
	})
}

// 执行地点列表接口
// 获取不分页
export function getQueryExeAddressList(params) {
	return request({
		url: `${plan}/queryExeAddressList`,
		method: 'get',
		params,
	})
}

// 获取二级项目下的机型列表信息
export function getProjectSecondModelListApi(params) {
	return request({
		url: `${plan}/queryPlanProjectSecondModelList`,
		method: 'get',
		params,
	})
}

// 获取备注描述(成品描述)
export function getRemarkDesctApi(params) {
	return request({
		url: `${plan}/getRemarkDesc`,
		method: 'get',
		params,
	})
}

// 复制接口
export function redactCopyApi(params) {
	return request({
		url: `${baseUrl}/copy`,
		method: 'post',
		params,
	})
}
// 解决终端警告
export const delBatchProcess = () => {}
export const syncContractAlls = () => {}
export const syncContractIncr = () => {}
export const getContractInfoCountsByDataStatus = () => {}
export const excelOfIdsUrl = ''
export const excelOfIdsUrls = ''
