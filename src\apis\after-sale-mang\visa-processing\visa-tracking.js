// 项目签证跟踪API
import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-sms-visa-stat-v1`

// 分页查询接口(原始)
export function getPageList(params) {
	return request({
		url: `${baseUrl}/page`,
		method: 'get',
		params,
	})
}

// 分页查询接口(项目签证跟踪)
export function getProjectVisaPageList(params) {
	return request({
		url: `${baseUrl}/pageTrack`,
		method: 'get',
		params,
	})
}
// 获取不分页
export function getAllList(params) {
	return request({
		url: `${baseUrl}/list`,
		method: 'get',
		params,
	})
}
// 保存草稿
export function addDraft(data) {
	return request({
		url: `${baseUrl}/add`,
		method: 'post',
		data,
	})
}
// 切换数据状态
export function statusChange(data) {
	return request({
		url: `${baseUrl}/statusChange`,
		method: 'post',
		data,
	})
}
// 修改
export function edit(data) {
	return request({
		url: `${baseUrl}/update`,
		method: 'put',
		data,
	})
}
// 发起流程
export function startProcess(data) {
	return request({
		url: `${baseUrl}/startProcessByCode`,
		method: 'post',
		data,
	})
}
// 详情接口
export function getDetail(id) {
	return request({
		url: `${baseUrl}/detail`,
		method: 'get',
		params: {
			id,
		},
	})
}
// 删除流程
export function deleteProcess(id) {
	return request({
		url: `${baseUrl}/delete`,
		method: 'delete',
		params: {
			id,
		},
	})
}
// 批量删除流程
export function delBatchProcess(ids) {
	return request({
		url: `${baseUrl}/delete/batch`,
		method: 'delete',
		params: {
			ids,
		},
	})
}

export const excelOfIdsUrl = `${baseUrl}/excel/exportIds`
export const excelOfFiltersUrl = `${baseUrl}/excel/export`
// // 导出excel
// export function exportProcess(params) {
// 	return request({
// 		url: `${baseUrl}/excel/export`,
// 		method: 'post',
// 		params,
// 		filterData: false,
// 		responseType: 'blob',
// 	})
// }
// // 导出选择excel
// export function exportProcessByIds(list) {
// 	return request({
// 		url: `${baseUrl}/excel/exportByIds`,
// 		method: 'post',
// 		data: list,
// 		filterData: false,
// 		responseType: 'blob',
// 	})
// }
