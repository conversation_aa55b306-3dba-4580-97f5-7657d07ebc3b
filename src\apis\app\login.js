import { request } from '@/apis/request'

// 退出登录
export function logout() {
  return request({
    url: `/oauth2/revoke`,
    method: 'get',
  })
}

// 更改信息
export function updatePassword(params) {
  return request({
    url: `/hpcc-ipop/v1/app/currentUser/updatePwd`,
    method: 'post',
    data: params
  })
}
// 更改头像
export function updateProfilePicture(params) {
  return request({
    url: `/hpcc-ipop/v1/app/currentUser/updateProfilePicture`,
    method: 'post',
    data: params
  })
}

