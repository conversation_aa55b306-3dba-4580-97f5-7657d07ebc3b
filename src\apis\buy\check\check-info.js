import {request} from '@/apis/request'
// import {request} from '@/apis/devAxios'
export function getList(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-wms-receipts-material-qc-v1/page`,
		method: 'get',
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
export function getAllList(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-wms-receipts-material-qc-v1/list`,
		method: 'get',
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
export const getCounts = (params)=>{
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-wms-receipts-material-qc-v1/status/count`,
		method: 'get',
		params,
	})
}
//新增接口
export function add(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-wms-receipts-material-qc-v1/add`,
		method: 'post',
		data,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
// 批量新增接口
export function adds({data,params}){
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-wms-receipts-material-qc-v1/add/batch/order/material`,
		method: 'post',
		data,
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
export const addIds = ({params,data})=>{
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-wms-receipts-material-qc-v1/add/material/procure/plan`,
		method: 'post',
		data,
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//编辑接口
export function edit(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-wms-receipts-material-qc-v1/update`,
		method: 'put',
		data,
		hideLoading: true,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//查询单条详情
export const getDetail = params => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-wms-receipts-material-qc-v1/detail`,
		method: 'get',
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//批量删除
export function deleteRows(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-wms-receipts-material-qc-v1/logicDelete/batch`,
		method: 'delete',
		params,
	})
}
export const excelOfIdsUrl = `/hpcc-ipop/v1_0/module/ipop-wms-receipts-material-qc-v1/excel/exportIds`
export const excelOfFiltersUrl = `/hpcc-ipop/v1_0/module/ipop-wms-receipts-material-qc-v1/excel/export`

export default {
	getList,
	getAllList,
	getDetail,
	add,
	edit,
	deleteRows,
	getCounts,
	excelOfIdsUrl,
	excelOfFiltersUrl,
}