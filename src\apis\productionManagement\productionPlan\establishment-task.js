// 编制任务
import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-plan-task-config-v1`
// 获取分页列表
export function getPageList(params) {
	return request({
		url: `${baseUrl}/page`,
		method: 'get',
		params,
	})
}
// 获取已存在的类型
export function getAlreadyManageType(params) {
	return request({
		url: `${baseUrl}/beforeMonthList`,
		method: 'get',
		params,
	})
}

// 获取详情
export function getDetail(params) {
	return request({
		url: `${baseUrl}/detail`,
		method: 'get',
		params,
	})
}

// 详情编制任务列表接口
export function getDetailList(params) {
	return request({
		url: `${baseUrl}/list`,
		method: 'get',
		params,
	})
}

//批量删除
export function deleteRows(params) {
	return request({
		url: `${baseUrl}/logicDelete/batch`,
		method: 'delete',
		params,
	})
}

// 提交任务信息接口
export function submitApi(data) {
	return request({
		url: `${baseUrl}/submitPlanTaskConfig`,
		method: 'post',
		data,
	})
}


// 保存草稿新增
export function addDraft(data) {
	return request({
		url: `${baseUrl}/add`,
		method: 'post',
		data,
	})
}

// 验证计划下的任务是否都完成
export function validateTaskFinish(params) {
	return request({
		url: `${baseUrl}/validateTaskFinish`,
		method: 'GET',
		params,
	})
}

// 修改
export function edit(data) {
	return request({
		url: `${baseUrl}/update`,
		method: 'put',
		data,
	})
}

// 修改状态为编制中接口
export function updateTaskStatusToOne(params) {
	return request({
		url: `${baseUrl}/updateTaskStatusToOne`,
		method: 'put',
		params,
	})
}

// 任务完成接口
export function finish(params) {
	return request({
		url: `${baseUrl}/finish`,
		method: 'put',
		params,
	})
}

// 任务驳回接口
export function reject(params) {
	return request({
		url: `${baseUrl}/reject`,
		method: 'put',
		params,
	})
}


// 发起流程
export function startProcess(data) {
	return request({
		url: `${baseUrl}/startProcessByCode`,
		method: 'post',
		data,
	})
}

export const excelOfIdsUrl = `${baseUrl}/excel/exportIds`
export const excelOfFiltersUrl = `${baseUrl}/excel/export`

// // 导出excel
// export function exportProcess(params) {
// 	return request({
// 		url: `${baseUrl}/excel/export`,
// 		method: 'post',
//     params,
// 		showResponseHeaders: true,
// 		responseType: 'blob',
// 	})
// }
// // 导出选择excel
// export function exportProcessByIds(data) {
// 	return request({
// 		url: `${baseUrl}/excel/exportIds`,
// 		method: 'post',
// 		data,
// 		showResponseHeaders: true,
// 		responseType: 'blob',
// 	})
// }



