import {request} from '@/apis/request'
// import {request} from '@/apis/devAxios'
export function getList(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-framework-contract-v1/page`,
		method: 'get',
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
export const getCounts = (params)=>{
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-framework-contract-v1/status/count`,
		method: 'get',
		params,
	})
}
//新增接口
export function add(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-framework-contract-v1/add`,
		method: 'post',
		data,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//编辑接口
export function edit(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-framework-contract-v1/update`,
		method: 'put',
		data,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//查询单条详情
export const getDetail = params => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-framework-contract-v1/detail`,
		method: 'get',
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//批量删除
export function deleteRows(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-framework-contract-v1/logicDelete/batch`,
		method: 'delete',
		params,
	})
}

//发起流程
export const startProcess = data => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-framework-contract-v1/startProcessByCode`,
		method: 'post',
		data,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
export const excelOfIdsUrl = `/hpcc-ipop/v1_0/module/ipop-srm-material-framework-contract-v1/excel/exportIds`
export const excelOfFiltersUrl = `/hpcc-ipop/v1_0/module/ipop-srm-material-framework-contract-v1/excel/export`

export default {
	getList,
	getDetail,
	add,
	edit,
	deleteRows,
	getCounts,
	startProcess,
	excelOfIdsUrl,
	excelOfFiltersUrl,
}