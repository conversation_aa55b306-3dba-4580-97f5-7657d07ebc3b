import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
export function getList(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/page`,
		method: 'get',
		params,
		isFormatParams: true,
		isFormatResponse: true,
	})
}
export function getAllList(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/list`,
		method: 'get',
		params,
		isFormatParams: true,
		isFormatResponse: true,
	})
}
export const getCounts = params => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/status/count`,
		method: 'get',
		params,
	})
}
//新增接口
export function add(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/add`,
		method: 'post',
		data,
		isFormatParams: true,
		isFormatResponse: true,
	})
}
// 批量新增接口--废弃
export function adds(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/add/batch`,
		method: 'post',
		data,
		isFormatParams: true,
		isFormatResponse: true,
	})
}
export const addIds = ({ params, data }) => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/add/batch//material/procure/plan`,
		method: 'post',
		data,
		params,
		isFormatParams: true,
		isFormatResponse: true,
	})
}
export const addIdsByContract = ({ params, data }) => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/add/batch/material/contract/material`,
		method: 'post',
		data,
		params,
		isFormatParams: true,
		isFormatResponse: true,
	})
}
//编辑接口
export function edit(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/update`,
		method: 'put',
		data,
		hideLoading: true,
		isFormatParams: true,
		isFormatResponse: true,
	})
}
//查询单条详情
export const getDetail = params => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/detail`,
		method: 'get',
		params,
		isFormatParams: true,
		isFormatResponse: true,
	})
}
//批量删除
export function deleteRows(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/logicDelete/batch`,
		method: 'delete',
		data,
	})
}
export const getMaterialTotal = params => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/total`,
		method: 'get',
		params,
	})
}
export const excelOfIdsUrl = `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/excel/exportIds`
export const excelOfFiltersUrl = `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/excel/export`
// 导出修改
export const excelOfIdsUrlEdit = `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/update/excel/exportIds`
export const excelOfFiltersUrlEdit = `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/update/excel/export`
// 导入修改数据
export const uploadExcelUrlEdit = `/hpcc-ipop/v1_0/module/ipop-srm-material-order-material-v1/update/excel/parse`

export default {
	getList,
	getDetail,
	add,
	edit,
	deleteRows,
	getCounts,
	excelOfIdsUrl,
	excelOfFiltersUrl,
}
