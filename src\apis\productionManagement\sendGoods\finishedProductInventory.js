// 发货单
import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-pmm-invoice-v1`

// 获取分页列表
export function getPageList(params) {
	return request({
		url: `${baseUrl}/page`,
		method: 'get',
		params,
	})
}
// 获取表格下方统计数据
export function getCommonTotal(params) {
	return request({
		url: `${baseUrl}/commonTotal`,
		method: 'get',
		params,
	})
}
// 获取发货数量
export function getPageCount(params) {
	return request({
		url: `${baseUrl}/pageCount`,
		method: 'get',
		params,
	})
}

// 发货单查询列表接口
export function getAllList(params) {
	return request({
		url: `${baseUrl}/list`,
		method: 'get',
		params,
	})
}

// 成品库存分页查询接口
export function getproductPageList(params) {
	return request({
		url: `${baseUrl}/orderInItemsByPage`,
		method: 'get',
		params,
	})
}

// 弹窗选择分页(二级项目)
export function getSecondProjectPageList(params) {
	return request({
		url: `${baseUrl}/projectSecondPageForPopWindow`,
		method: 'get',
		params,
	})
}

// 根据项目编码获取二级项目信息
export function getSecondProjectInfoPageList(params) {
	return request({
		url: `${baseUrl}/getContractInfoByCode`,
		method: 'get',
		params,
	})
}

// 根据项目编码获取二级项目信息(主要用户判断项目的产品类型)
export function getSecondProjectInfoApi(params) {
	return request({
		url: `${baseUrl}/getPlanProjectSecondByCode`,
		method: 'get',
		params,
	})
}

// 获取物资领用审核状态数量统计接口
export function getPageListCountApi(params) {
	return request({
		url: `${baseUrl}/getPageDataStatusCount`,
		method: 'get',
		params,
	})
}

// 新增
export function add(data) {
	return request({
		url: `${baseUrl}/add`,
		method: 'post',
		data,
	})
}

// 修改
export function edit(data) {
	return request({
		url: `${baseUrl}/update`,
		method: 'put',
		data,
	})
}

// 审批时验证发货出库的出库数量（库存量是否足够）
export function validateProductNum(data) {
	return request({
		url: `${baseUrl}/validateStockQuality`,
		method: 'POST',
		data,
	})
}

// 详情接口
export function getDetail(params) {
	return request({
		url: `${baseUrl}/detail`,
		method: 'get',
		params,
	})
}

// 逻辑删除接口, ids以逗号分隔多个id
export function deleteRows(params) {
	return request({
		url: `${baseUrl}/logicDelete/batch`,
		method: 'DELETE',
		params,
	})
}

/** 发起流程 */
export function startProcess(data) {
	return request({
		url: `${baseUrl}/startProcessByCode`,
		method: 'post',
		data,
	})
}

/** 生成 */
export function generate(data) {
	return request({
		url: `${baseUrl}/generate`,
		method: 'post',
		data,
	})
}

export const excelOfIdsUrl = `${baseUrl}/excel/exportIds`
export const excelOfFiltersUrl = `${baseUrl}/excel/export`
export const product_excelOfIdsUrl = `${baseUrl}/excel/exportIds/orderItem`
export const product_excelOfFiltersUrl = `${baseUrl}/excel/export/OrderItem`
export const generateUrl = `${baseUrl}/generate`
// // 导出excel
// export function exportProcess(params) {
// 	return request({
// 		url: `${baseUrl}/excel/export`,
// 		method: 'post',
//     params,
// 		showResponseHeaders: true,
// 		responseType: 'blob',
// 	})
// }
// // 导出选择excel
// export function exportProcessByIds(data) {
// 	return request({
// 		url: `${baseUrl}/excel/exportIds`,
// 		method: 'post',
// 		data,
// 		showResponseHeaders: true,
// 		responseType: 'blob',
// 	})
// }
