<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="utf-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="Author" content="MMclaughlin" />
		<meta name="Translate" content="<EMAIL>" />
		<meta name="keywords" content="decimal.js,中文版,翻译,<EMAIL>" />
		<title>decimal.js 中文API</title>
		<link href="../style.css" rel="stylesheet" />
	</head>
	<body>
		<h1 class="hide">decimal.js 中文版 翻译作者 <EMAIL></h1>

		<div class="nav">
			<a class="nav-title" href="#">API说明</a>

			<b>构造函数</b>
			<ul>
				<li>
					<a href="#decimal"><strong>Decimal</strong></a>
				</li>
			</ul>

			<a href="#methods">Methods 方法</a>
			<ul>
				<li><a href="#Dabs">abs（绝对值）</a></li>
				<li><a href="#Dacos">acos（反余弦值）</a></li>
				<li><a href="#Dacosh">acosh（反双曲余弦值）</a></li>
				<li><a href="#Dadd">add（加法）</a></li>
				<li><a href="#Dasin">asin（反正弦值）</a></li>
				<li><a href="#Dasinh">asinh（反双曲正弦值）</a></li>
				<li><a href="#Datan">atan（反正切值）</a></li>
				<li><a href="#Datanh">atanh（反双曲正切值）</a></li>
				<li><a href="#Datan2">atan2（反正切值增强）</a></li>
				<li><a href="#Dcbrt">cbrt（立方根）</a></li>
				<li><a href="#Dceil">ceil（上入整数）</a></li>
				<li><a href="#Dclone">clone（拷贝）</a></li>
				<li><a href="#Dcos">cos（余弦值）</a></li>
				<li><a href="#Dcosh">cosh（双曲余弦值）</a></li>
				<li><a href="#Ddiv">div（除法）</a></li>
				<li><a href="#Dexp">exp（自然指数）</a></li>
				<li><a href="#Dfloor">floor（向下取整）</a></li>
				<li><a href="#Dhypot">hypot（欧几里德范数）</a></li>
				<li><a href="#DisDecimal">isDecimal（是否为Decimal）</a></li>
				<li><a href="#Dln">ln（自然对数）</a></li>
				<li><a href="#Dlog">log（对数）</a></li>
				<li><a href="#Dlog2">log2（2底对数）</a></li>
				<li><a href="#Dlog10">log10（10底对数）</a></li>
				<li><a href="#Dmax">max（最大值）</a></li>
				<li><a href="#Dmin">min（最小值）</a></li>
				<li><a href="#Dmod">mod（取模）</a></li>
				<li><a href="#Dmul">mul（乘法）</a></li>
				<li><a href="#DnoConflict">noConflict</a></li>
				<li><a href="#Dpow">pow（幂）</a></li>
				<li><a href="#Drandom">random（随机数）</a></li>
				<li><a href="#Dround">round（四舍五入）</a></li>
				<li><a href="#Dset">set（设置）</a></li>
				<li><a href="#Dsign">sign（符号函数）</a></li>
				<li><a href="#Dsin">sin（正弦值）</a></li>
				<li><a href="#Dsinh">sinh（双曲正弦值）</a></li>
				<li><a href="#Dsqrt">sqrt（平方根）</a></li>
				<li><a href="#Dsub">sub（减法）</a></li>
				<li><a href="#Dtan">tan（正切值）</a></li>
				<li><a href="#Dtanh">tanh（双曲正切值）</a></li>
				<li><a href="#Dtrunc">trunc（去除小数）</a></li>
			</ul>

			<a href="#constructor-properties">Properties 属性</a>
			<ul>
				<li><a href="#precision">precision（精度）</a></li>
				<li><a href="#rounding">rounding（舍入）</a></li>
				<li><a href="#minE">minE（最小指数）</a></li>
				<li><a href="#maxE">maxE（最大指数）</a></li>
				<li><a href="#toExpNeg">toExpNeg（负指数值）</a></li>
				<li><a href="#toExpPos">toExpPos（正指数值）</a></li>
				<li><a href="#modulo">modulo（模态）</a></li>
				<li><a href="#crypto">crypto（加密）</a></li>
				<li class="spacer">&nbsp;</li>
				<li><a href="#modes">ROUND_UP（远离零）</a></li>
				<li><a href="#modes">ROUND_DOWN（靠近零）</a></li>
				<li><a href="#modes">ROUND_CEIL（靠近+∞）</a></li>
				<li><a href="#modes">ROUND_FLOOR（靠近-∞）</a></li>
				<li><a href="#modes">ROUND_HALF_UP（向上靠近最近值）</a></li>
				<li><a href="#modes">ROUND_HALF_DOWN（向下靠近最近值）</a></li>
				<li><a href="#modes">ROUND_HALF_EVEN（最近值四舍五入）</a></li>
				<li><a href="#modes">ROUND_HALF_CEIL（最近值靠近+∞）</a></li>
				<li><a href="#modes">ROUND_HALF_FLOOR（最近值靠近-∞）</a></li>
				<li><a href="#modes">EUCLID（欧几里德）</a></li>
			</ul>

			<b>INSTANCE 实例</b>

			<a href="#prototype-methods">Methods 方法</a>
			<ul>
				<li>
					<a href="#abs">absoluteValue</a>
					<span>abs</span>
				</li>
				<li><a href="#ceil">ceil</a></li>
				<li>
					<a href="#cmp">comparedTo</a>
					<span>cmp</span>
				</li>
				<li>
					<a href="#cos">cosine</a>
					<span>cos</span>
				</li>
				<li>
					<a href="#cbrt">cubeRoot</a>
					<span>cbrt</span>
				</li>
				<li>
					<a href="#dp">decimalPlaces</a>
					<span>dp</span>
				</li>
				<li>
					<a href="#div">dividedBy</a>
					<span>div</span>
				</li>
				<li>
					<a href="#divToInt">dividedToIntegerBy</a>
					<span>divToInt</span>
				</li>
				<li>
					<a href="#eq">equals</a>
					<span>eq</span>
				</li>
				<li><a href="#floor">floor</a></li>
				<li>
					<a href="#gt">greaterThan</a>
					<span>gt</span>
				</li>
				<li>
					<a href="#gte">greaterThanOrEqualTo</a>
					<span>gte</span>
				</li>
				<li>
					<a href="#cosh">hyperbolicCosine</a>
					<span>cosh</span>
				</li>
				<li>
					<a href="#sinh">hyperbolicSine</a>
					<span>sinh</span>
				</li>
				<li>
					<a href="#tanh">hyperbolicTangent</a>
					<span>tanh</span>
				</li>
				<li>
					<a href="#acos">inverseCosine</a>
					<span>acos</span>
				</li>
				<li>
					<a href="#acosh">inverseHyperbolicCosine</a>
					<span>acosh</span>
				</li>
				<li>
					<a href="#asinh">inverseHyperbolicSine</a>
					<span>asinh</span>
				</li>
				<li>
					<a href="#atanh">inverseHyperbolicTangent</a>
					<span>atanh</span>
				</li>
				<li>
					<a href="#asin">inverseSine</a>
					<span>asin</span>
				</li>
				<li>
					<a href="#atan">inverseTangent</a>
					<span>atan</span>
				</li>
				<li><a href="#isFinite">isFinite</a></li>
				<li>
					<a href="#isInt">isInteger</a>
					<span>isInt</span>
				</li>
				<li><a href="#isNaN">isNaN</a></li>
				<li>
					<a href="#isNeg">isNegative</a>
					<span>isNeg</span>
				</li>
				<li>
					<a href="#isPos">isPositive</a>
					<span>isPos</span>
				</li>
				<li><a href="#isZero">isZero</a></li>
				<li>
					<a href="#lt">lessThan</a>
					<span>lt</span>
				</li>
				<li>
					<a href="#lte">lessThanOrEqualTo</a>
					<span>lte</span>
				</li>
				<li>
					<a href="#log">logarithm</a>
					<span>log</span>
				</li>
				<li>
					<a href="#sub">minus</a>
					<span>sub</span>
				</li>
				<li>
					<a href="#mod">modulo</a>
					<span>mod</span>
				</li>
				<li>
					<a href="#exp">naturalExponential</a>
					<span>exp</span>
				</li>
				<li>
					<a href="#ln">naturalLogarithm</a>
					<span>ln</span>
				</li>
				<li>
					<a href="#neg">negated</a>
					<span>neg</span>
				</li>
				<li>
					<a href="#add">plus</a>
					<span>add</span>
				</li>
				<li>
					<a href="#sd">precision</a>
					<span>sd</span>
				</li>
				<li><a href="#round">round</a></li>
				<li>
					<a href="#sin">sine</a>
					<span>sin</span>
				</li>
				<li>
					<a href="#sqrt">squareRoot</a>
					<span>sqrt</span>
				</li>
				<li>
					<a href="#tan">tangent</a>
					<span>tan</span>
				</li>
				<li>
					<a href="#mul">times</a>
					<span>mul</span>
				</li>
				<li><a href="#toBinary">toBinary</a></li>
				<li>
					<a href="#toDP">toDecimalPlaces</a>
					<span>toDP</span>
				</li>
				<li><a href="#toExponential">toExponential</a></li>
				<li><a href="#toFixed">toFixed</a></li>
				<li><a href="#toFraction">toFraction</a></li>
				<li>
					<a href="#toHex">toHexadecimal</a>
					<span>toHex</span>
				</li>
				<li><a href="#toJSON">toJSON</a></li>
				<li><a href="#toNearest">toNearest</a></li>
				<li><a href="#toNumber">toNumber</a></li>
				<li><a href="#toOctal">toOctal</a></li>
				<li>
					<a href="#pow">toPower</a>
					<span>pow</span>
				</li>
				<li><a href="#toPrecision">toPrecision</a></li>
				<li>
					<a href="#toSD">toSignificantDigits</a>
					<span>toSD</span>
				</li>
				<li><a href="#toString">toString</a></li>
				<li>
					<a href="#trunc">truncated</a>
					<span>trunc</span>
				</li>
				<li><a href="#valueOf">valueOf</a></li>
			</ul>

			<a href="#instance-properties">Properties 属性</a>
			<ul>
				<li>
					<a href="#digits">d</a>
					<span>digits</span>
				</li>
				<li>
					<a href="#exponent">e</a>
					<span>exponent</span>
				</li>
				<li>
					<a href="#sign">s</a>
					<span>sign</span>
				</li>
			</ul>

			<a href="#zero-nan-infinity">零, NaN &amp; Infinity</a>
			<a href="#Errors">错误</a>
			<a href="#Pi">Pi</a>
			<a class="end" href="#faq">问题解答</a>
		</div>

		<div class="container">
			<h1>
				decimal
				<span id="js">.js</span>
			</h1>

			<p>一个的任意精度十进制类型JavaScript库. （翻译 <EMAIL>）</p>
			<p>
				<a href="https://github.com/MikeMcl/decimal.js" target="_blank">GitHub</a>
				|
				<a href="https://gitee.com/mirrors_addons/decimal.js" target="_blank">Gitee</a>
			</p>

			<h2>API</h2>

			<p>
				在 Gitee 的
				<a href="https://gitee.com/mirrors_addons/decimal.js" target="_blank">README</a>
				上有快速入门的介绍。
			</p>
			<p>
				在下面的所有示例中,
				<code>var</code>
				和分号不显示, 如果注释后面的内容带有引号表示已经调用了
				<code>toString</code>
				方法。
			</p>
			<br />
			<p>
				在加载库时，会自动根据Decimal的构造函数定义一个
				<a href="#decimal"><code>Decimal</code></a>
				函数。
			</p>
			<p>
				<i>如果有需要，你可以创建多个Decimal构造函数，每个构造函数都独享自己的配置，例如精度、范围，这些配置只对当前构造函数创建的实例有效。</i>
			</p>
			<p>
				<i>
					可以通过调用已经存在Decimal构造函数的
					<code><a href="#Dclone">clone</a></code>
					来产生一个新的Decimal构造函数。
				</i>
			</p>

			<h3 class="end">构造函数</h3>

			<h5 id="decimal">
				Decimal
				<code class="inset">
					Decimal(value)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<dl>
				<dt>
					<code>value</code>
					:
					<i>number|string|Decimal</i>
				</dt>
				<dd>
					一个合法的
					<code>value</code>
					可以是 integer 或者 float 类型的数字或者字符串, 包括
					<code>&plusmn;0</code>
					、
					<code>&plusmn;Infinity</code>
					、
					<code>NaN</code>
					.
				</dd>
				<dd>
					<code>value</code>
					没有位数限制，JavaScript 数组的最大值和实际需要的处理时间除外。
				</dd>
				<dd>
					<code>value</code>
					的允许范围参见最大指数
					<a href="#maxE">maxE</a>
					，最小指数
					<a href="#minE">minE</a>
					.
				</dd>
				<dd>
					和十进制的数字一样，如果数据包含合法的前缀也可初始化， 比如： 十六进制前缀为
					<code>0x</code>
					或
					<code>0X</code>
					， 二进制前缀为
					<code>0b</code>
					或者
					<code>0B</code>
					， 八进制前缀为
					<code>0o</code>
					或者
					<code>0O</code>
					。
				</dd>
				<dd>十进制和其它进制的数据都可以使用指数法（浮点数）和普通形式表示。</dd>
				<dd>
					使用指数法表示时
					<code>e</code>
					或者
					<code>E</code>
					表示十进制的十进制幂，
					<code>p</code>
					或者
					<code>P</code>
					表示非十进制的二次幂。
				</dd>
			</dl>
			<p>Returns: 一个新的Decimal对象的实例。</p>
			<p>
				Throws: 无效的
				<code>value</code>
				.
			</p>
			<pre>
x = new Decimal(9)                       // '9'
y = new Decimal(x)                       // '9'

new Decimal('5032485723458348569331745.33434346346912144534543')
new Decimal('4.321e+4')                  // '43210'
new Decimal('-735.0918e-430')            // '-7.350918e-428'
new Decimal('5.6700000')                 // '5.67'
new Decimal(Infinity)                    // 'Infinity'
new Decimal(NaN)                         // 'NaN'
new Decimal('.5')                        // '0.5'
new Decimal('-0b10110100.1')             // '-180.5'
new Decimal('0xff.8')                    // '255.5'

new Decimal(0.046875)                    // '0.046875'
new Decimal('0.046875000000')            // '0.046875'

new Decimal(4.6875e-2)                   // '0.046875'
new Decimal('468.75e-4')                 // '0.046875'

new Decimal('0b0.000011')                // '0.046875'
new Decimal('0o0.03')                    // '0.046875'
new Decimal('0x0.0c')                    // '0.046875'

new Decimal('0b1.1p-5')                  // '0.046875'
new Decimal('0o1.4p-5')                  // '0.046875'
new Decimal('0x1.8p-5')                  // '0.046875'</pre>

			<h4 id="methods">Methods 方法</h4>
			<p>Decimal 构造函数的方法。</p>

			<h5 id="Dabs">
				abs（绝对值）
				<code class="inset">
					.abs(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#abs">absoluteValue</a></code>
				.
			</p>
			<pre>
a = Decimal.abs(x)
b = new Decimal(x).abs()
a.equals(b)                    // true</pre>

			<h5 id="Dacos">
				acos（反余弦值）
				<code class="inset">
					.acos(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#acos">inverseCosine</a></code>
				.
			</p>
			<pre>
a = Decimal.acos(x)
b = new Decimal(x).acos()
a.equals(b)                    // true</pre>

			<h5 id="Dacosh">
				acosh（反双曲余弦值）
				<code class="inset">
					.acosh(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#acos">inverseHyperbolicCosine</a></code>
				.
			</p>
			<pre>
a = Decimal.acosh(x)
b = new Decimal(x).acosh()
a.equals(b)                    // true</pre>

			<h5 id="Dadd">
				add（加法）
				<code class="inset">
					.add(x, y)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
				<br />
				<code>y</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				<code><a href="#add">plus</a></code>
				别名.
			</p>
			<pre>
a = Decimal.add(x, y)
b = new Decimal(x).plus(y)
a.equals(b)                    // true</pre>

			<h5 id="Dasin">
				asin（反正弦值）
				<code class="inset">
					.asin(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#asin">inverseSine</a></code>
				.
			</p>
			<pre>
a = Decimal.asin(x)
b = new Decimal(x).asin()
a.equals(b)                    // true</pre>

			<h5 id="Dasinh">
				asinh（反双曲正弦值）
				<code class="inset">
					.asinh(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#asin">inverseHyperbolicSine</a></code>
				.
			</p>
			<pre>
a = Decimal.asinh(x)
b = new Decimal(x).asinh()
a.equals(b)                    // true</pre>

			<h5 id="Datan">
				atan（反正切值）
				<code class="inset">
					.atan(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#atan">inverseTangent</a></code>
				.
			</p>
			<pre>
a = Decimal.atan(x)
b = new Decimal(x).atan()
a.equals(b)                    // true</pre>

			<h5 id="Datanh">
				atanh（反双曲正切值）
				<code class="inset">
					.atanh(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#atan">inverseHyperbolicTangent</a></code>
				.
			</p>
			<pre>
a = Decimal.atanh(x)
b = new Decimal(x).atanh()
a.equals(b)                    // true</pre>

			<h5 id="Datan2">
				atan2（反正切值增强）
				<code class="inset">
					.atan2(y, x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>y</code>
				:
				<i>number|string|Decimal</i>
				<br />
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				计算
				<code>y</code>
				和
				<code>x</code>
				的反正切值， 并使用
				<a href="#rounding"><code>rounding（舍入）</code></a>
				模式，
				<a href="#precision"><code>precision（精确）</code></a>
				到有效数字， 然后返回一个新的 Decimal对象。
			</p>

			<p>
				<code>y</code>
				和
				<code>x</code>
				的符号用于确定结果所在的象限。
			</p>
			<p>
				取值范围: [
				<code>-Infinity, Infinity</code>
				]
				<br />
				值范围: [
				<code>-pi, pi</code>
				]
			</p>
			<p>
				值范围查看
				<a href="#Pi"><code>Pi</code></a>
				，取值范围查看
				<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/atan2" target="_blank">
					<code>Math.atan2()</code>
					.
				</a>
			</p>
			<pre>r = Decimal.atan2(y, x)</pre>

			<h5 id="Dcbrt">
				cbrt（立方根）
				<code class="inset">
					.cbrt(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#cbrt">cubeRoot</a></code>
				.
			</p>
			<pre>
a = Decimal.cbrt(x)
b = new Decimal(x).cbrt()
a.equals(b)                    // true</pre>

			<h5 id="Dceil">
				ceil（上入整数）
				<code class="inset">
					.ceil(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#ceil">ceil</a></code>
				.
			</p>
			<pre>
a = Decimal.ceil(x)
b = new Decimal(x).ceil()
a.equals(b)                    // true</pre>

			<h5 id="Dclone">
				clone（拷贝）
				<code class="inset">
					.clone([object])
					<i>&rArr; Decimal 构造函数</i>
				</code>
			</h5>
			<p>
				<code>object</code>
				:
				<i>object</i>
			</p>
			<p>
				返回一个新的Decimal构造函数，该构造函数具有描述对象的设置（请参阅
				<a href="#Dset"><code>set</code></a>
				）， 如果省略了
				<code>object</code>
				，则和原来的Decimal构造函数具有相同的设置。
			</p>
			<pre>
Decimal.set({ precision: 5 })
Decimal9 = Decimal.clone({ precision: 9 })

a = new Decimal(1)
b = new Decimal9(1)

a.div(3)                           // 0.33333
b.div(3)                           // 0.333333333

// 相当于 Decimal9 = Decimal.clone({ precision: 9 }):
Decimal9 = Decimal.clone()
Decimal9.set({ precision: 9 })</pre>

			<p>
				如果
				<code>object</code>
				的
				<code>'defaults'</code>
				属性为
				<code>true</code>
				，那么新的构造函数将使用默认的配置。
			</p>
			<pre>
D1 = Decimal.clone({ defaults: true })

// 使用默认值（精度precision属性除外）
D2 = Decimal.clone({ defaults: true, precision: 50 })</pre
			>

			<p>使用多个 Decimal 构造函数，并不会影响内存使用效率，因为它们之间的功能是共享的。</p>

			<h5 id="Dcos">
				cos（余弦值）
				<code class="inset">
					.cos(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#cos">cosine</a></code>
				.
			</p>
			<pre>
a = Decimal.cos(x)
b = new Decimal(x).cos()
a.equals(b)                    // true</pre>

			<h5 id="Dcosh">
				cosh（双曲余弦值）
				<code class="inset">
					.cosh(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#cos">hyperbolicCosine</a></code>
				.
			</p>
			<pre>
a = Decimal.cosh(x)
b = new Decimal(x).cosh()
a.equals(b)                    // true</pre
			>

			<h5 id="Ddiv">
				div（除法）
				<code class="inset">
					.div(x, y)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
				<br />
				<code>y</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#div">dividedBy</a></code>
				.
			</p>
			<pre>
a = Decimal.div(x, y)
b = new Decimal(x).div(y)
a.equals(b)                    // true</pre
			>

			<h5 id="Dexp">
				exp（自然指数）
				<code class="inset">
					.exp(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#exp">naturalExponential</a></code>
				.
			</p>
			<pre>
a = Decimal.exp(x)
b = new Decimal(x).exp()
a.equals(b)                    // true</pre
			>

			<h5 id="Dfloor">
				floor（向下取整）
				<code class="inset">
					.floor(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#floor">floor</a></code>
				.
			</p>
			<pre>
a = Decimal.floor(x)
b = new Decimal(x).floor()
a.equals(b)                    // true</pre
			>

			<h5 id="Dhypot">
				hypot（欧几里德范数）
				<code class="inset">
					.hypot([x [, y, ...]])
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
				<br />
				<code>y</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				返回所有参数的平方和的平方根， 并使用
				<a href="#rounding">
					<code>rounding</code>
					（舍入）
				</a>
				模式，
				<a href="#precision">
					<code>precision</code>
					（精确）
				</a>
				到有效数字，然后返回一个新的 Decimal对象。
			</p>
			<pre>r = Decimal.hypot(x, y)</pre>

			<h5 id="Dln">
				ln（自然对数）
				<code class="inset">
					.ln(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#ln">naturalLogarithm</a></code>
				.
			</p>
			<pre>
a = Decimal.ln(x)
b = new Decimal(x).ln()
a.equals(b)                    // true</pre
			>

			<h5 id="DisDecimal">
				isDecimal（是否为Decimal）
				<code class="inset">
					.isDecimal(object)
					<i>&rArr; boolean</i>
				</code>
			</h5>
			<p>
				<code>object</code>
				:
				<i>任意</i>
			</p>
			<p>
				如果
				<code>object</code>
				是 Decimal 实例（其中Decimal是任何Decimal构造函数）， 则返回
				<code>true</code>
				；否则，返回
				<code>false</code>
				。
			</p>
			<pre>
a = new Decimal(1)
b = {}
a instanceof Decimal           // true
Decimal.isDecimal(a)           // true
Decimal.isDecimal(b)           // false</pre
			>

			<h5 id="Dlog">
				log（对数）
				<code class="inset">
					.log(x [, base])
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
				<br />
				<code>base</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#log">logarithm</a></code>
				.
			</p>
			<p>
				默认的底数是
				<code>10</code>
				，它与 JavaScript 的
				<code>Math.log()</code>
				不同，后者返回自然对数（底数
				<code>e</code>
				）。
			</p>
			<pre>
a = Decimal.log(x, y)
b = new Decimal(x).log(y)
a.equals(b)                    // true</pre
			>

			<h5 id="Dlog2">
				log2（2底对数）
				<code class="inset">
					.log2(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				返回
				<code>x</code>
				以
				<code>2</code>
				为底的对数， 并使用
				<a href="#rounding">
					<code>rounding</code>
					（舍入）
				</a>
				模式，
				<a href="#precision">
					<code>precision</code>
					（精确）
				</a>
				到有效数字，然后返回一个新的 Decimal对象。
			</p>
			<pre>r = Decimal.log2(x)</pre>

			<h5 id="Dlog10">
				log10（10底对数）
				<code class="inset">
					.log10(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				返回
				<code>x</code>
				以
				<code>10</code>
				为底的对数， 并使用
				<a href="#rounding">
					<code>rounding</code>
					（舍入）
				</a>
				模式，
				<a href="#precision">
					<code>precision</code>
					（精确）
				</a>
				到有效数字，然后返回一个新的 Decimal对象。
			</p>
			<pre>r = Decimal.log10(x)</pre>

			<h5 id="Dmax">
				max（最大值）
				<code class="inset">
					.max([x [, y, ...]])
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
				<br />
				<code>y</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>返回参数的最大值为一个新的Decimal.</p>
			<pre>r = Decimal.max(x, y, z)</pre>

			<h5 id="Dmin">
				min（最小值）
				<code class="inset">
					.min([x [, y, ...]])
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
				<br />
				<code>y</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>返回参数的最小值为一个新的Decimal.</p>
			<pre>r = Decimal.min(x, y, z)</pre>

			<h5 id="Dmod">
				mod（取模）
				<code class="inset">
					.mod(x, y)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
				<br />
				<code>y</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#mod">modulo</a></code>
				.
			</p>
			·
			<pre>
a = Decimal.mod(x, y)
b = new Decimal(x).mod(y)
a.equals(b)                    // true</pre
			>

			<h5 id="Dmul">
				mul（乘法）
				<code class="inset">
					.mul(x, y)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
				<br />
				<code>y</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#mul">times</a></code>
				.
			</p>
			<pre>
a = Decimal.mul(x, y)
b = new Decimal(x).mul(y)
a.equals(b)                    // true</pre
			>

			<h5 id="DnoConflict">
				noConflict
				<code class="inset">
					.noConflict()
					<i>&rArr; Decimal 构造函数</i>
				</code>
			</h5>
			<p><i>仅限于浏览器.</i></p>
			<p>
				将
				<code>Decimal</code>
				变量恢复为加载该库之前的值，并返回对原始Decimal构造函数的引用，以便可以将其分配给具有不同名称的变量。
			</p>
			<pre>
&lt;script&gt; Decimal = 1 &lt;/script&gt;
&lt;script src='/path/to/decimal.js'&gt;&lt;/script&gt;
&lt;script&gt;
  a = new Decimal(2)      // '2'
  D = Decimal.noConflict()
  Decimal                 // 1
  b = new D(3)            // '3'
&lt;/script&gt;</pre
			>

			<h5 id="Dpow">
				pow（幂）
				<code class="inset">
					.pow(base, exponent)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>base</code>
				:
				<i>number|string|Decimal</i>
				<br />
				<code>exponent</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#pow">toPower</a></code>
				.
			</p>
			<pre>
a = Decimal.pow(x, y)
b = new Decimal(x).pow(y)
a.equals(b)                    // true</pre
			>

			<h5 id="Drandom">
				random（随机数）
				<code class="inset">
					.random([dp])
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>dp</code>
				:
				<i>number</i>
				: integer，包括
				<code>0</code>
				到
				<code>1e+9</code>
			</p>
			<p>
				返回一个 大于等于
				<code>0</code>
				，小于
				<code>1</code>
				伪随机数的 Decimal 对象。
			</p>
			<p>
				返回
				<code>dp</code>
				位的小数，如果位数不足，直接返回小数位。 如果省略
				<code>dp</code>
				，小数位数为默认的
				<a href="#precision">
					<code>precision</code>
					精度
				</a>
				设置。
			</p>
			<p>
				如果当前 Decimal 的构造函数的属性
				<a href="#crypto"><code>crypto</code></a>
				的值为
				<code>true</code>
				， 并且
				<code>crypto</code>
				对象在环境中可用， 则由
				<code>crypto.getRandomValues</code>
				（现代浏览器中的Web密码API） 或者
				<code>crypto.randomBytes</code>
				（Node.js）来生成， 如果
				<a href="#crypto"><code>crypto</code></a>
				的属性值为
				<code>false</code>
				， 则返回值由
				<code>Math.random</code>
				（最快）生成。
			</p>
			<p>
				要使
				<code>crypto</code>
				对象在 Node.js 全局中可用，可以使用：
			</p>
			<pre>global.crypto = require('crypto')</pre>
			<p>
				如果
				<a href="#crypto"><code>crypto</code></a>
				设置为
				<code>true</code>
				， 但是
				<code>crypto</code>
				对象又不可用，将会抛出异常。
			</p>
			<p>
				如果使用
				<code>crypto（加密）</code>
				方法，则返回的十进制值是加密安全的，并且与随机值在统计上是无法区分的。
			</p>
			<pre>
Decimal.set({ precision: 10 })
Decimal.random()                    // '0.4117936847'
Decimal.random(20)                  // '0.78193327636914089009'</pre
			>

			<h5 id="Dround">
				round（四舍五入）
				<code class="inset">
					.round(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#round">round</a></code>
				.
			</p>
			<pre>
a = Decimal.round(x)
b = new Decimal(x).round()
a.equals(b)                    // true</pre
			>

			<h5 id="Dset">
				set（设置）
				<code class="inset">
					.set(object)
					<i>&rArr; Decimal 构造函数</i>
				</code>
			</h5>
			<p>
				<code>object</code>
				:
				<i>object</i>
			</p>
			<p>为指定的 Decimal 构造函数配置 '全局' 的参数，既对当前创建的 Decimal 实例生效。</p>
			<p>Returns: 当前 Decimal 对象的实例.</p>
			<p>
				配置对象
				<code>object</code>
				可以包含全部或者部分配置，这些属性在
				<a href="#constructor-properties">Properties</a>
				（属性）中有详细描述，以下有示例：
			</p>
			<p>检查配置对象属性的值的有效性，然后将其存储为此Decimal构造函数的同名属性。</p>
			<p>
				如果
				<code>object</code>
				的
				<code>'defaults'</code>
				为
				<code>true</code>
				，则所有未指定的属性都将重置为其默认值。
			</p>
			<p>Throws：无效的配置参数.</p>
			<pre>
// 默认值
Decimal.set({
    precision: 20,
    rounding: 4,
    toExpNeg: -7,
    toExpPos: 21,
    maxE: 9e15,
    minE: -9e15,
    modulo: 1,
    crypto: false
})

// 将所有属性重置为其默认值
Decimal.set({ defaults: true })

// 将precision设置为50，并将所有其他属性设置为其默认值
Decimal.set({ precision: 50, defaults: true })</pre
			>

			<p>也可以直接使用Decimal构造函数来设置，但是这回跳过参数有效性检查，如果你知道是如何进行有效配置，那么这不是问题。</p>
			<pre>Decimal.precision = 40</pre>

			<h5 id="Dsign">
				sign（符号函数）
				<code class="inset">
					.sign(x)
					<i>&rArr; number</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<table>
				<tr>
					<th>返回值</th>
					<th>&nbsp;</th>
				</tr>
				<tr>
					<td class="centre"><code>1</code></td>
					<td>
						<code>x</code>
						大于0
					</td>
				</tr>
				<tr>
					<td class="centre"><code>-1</code></td>
					<td>
						<code>x</code>
						小于0
					</td>
				</tr>
				<tr>
					<td class="centre"><code>0</code></td>
					<td>
						<code>x</code>
						等于正0
					</td>
				</tr>
				<tr>
					<td class="centre"><code>-0</code></td>
					<td>
						<code>x</code>
						等于负0
					</td>
				</tr>
				<tr>
					<td class="centre"><code>NaN</code></td>
					<td>
						<code>x</code>
						是
						<code>NaN</code>
					</td>
				</tr>
			</table>
			<pre>r = Decimal.sign(x)</pre>

			<h5 id="Dsin">
				sin（正弦值）
				<code class="inset">
					.sin(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#sin">sine</a></code>
				.
			</p>
			<pre>
a = Decimal.sin(x)
b = new Decimal(x).sin()
a.equals(b)                    // true</pre
			>

			<h5 id="Dsinh">
				sinh（双曲正弦值）
				<code class="inset">
					.sinh(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#sin">hyperbolicSine</a></code>
				.
			</p>
			<pre>
a = Decimal.sinh(x)
b = new Decimal(x).sinh()
a.equals(b)                    // true</pre
			>

			<h5 id="Dsqrt">
				sqrt（平方根）
				<code class="inset">
					.sqrt(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<a href="#sqrt">squareRoot</a>
				.
			</p>
			<pre>
a = Decimal.sqrt(x)
b = new Decimal(x).sqrt()
a.equals(b)                    // true</pre
			>

			<h5 id="Dsub">
				sub（减法）
				<code class="inset">
					.sub(x, y)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
				<br />
				<code>y</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#sub">minus</a></code>
				.
			</p>
			<pre>
a = Decimal.sub(x, y)
b = new Decimal(x).sub(y)
a.equals(b)                    // true</pre
			>

			<h5 id="Dtan">
				tan（正切值）
				<code class="inset">
					.tan(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#tan">tangent</a></code>
				.
			</p>
			<pre>
a = Decimal.tan(x)
b = new Decimal(x).tan()
a.equals(b)                    // true</pre
			>

			<h5 id="Dtanh">
				tanh（双曲正切值）
				<code class="inset">
					.tanh(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#tan">hyperbolicTangent</a></code>
				.
			</p>
			<pre>
a = Decimal.tanh(x)
b = new Decimal(x).tanh()
a.equals(b)                    // true</pre
			>

			<h5 id="Dtrunc">
				trunc（去除小数）
				<code class="inset">
					.trunc(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				详情查看
				<code><a href="#trunc">truncated</a></code>
				.
			</p>
			<pre>
a = Decimal.trunc(x)
b = new Decimal(x).trunc()
a.equals(b)                    // true</pre
			>

			<h4 id="constructor-properties">Properties 属性</h4>
			<p>Decimal 构造函数可配置的属性。</p>

			<h6 id="configProps">配置属性</h6>
			<p>
				使用set方法设置配置属性
				<a href="#precision"><code>precision</code></a>
				,
				<a href="#rounding"><code>rounding</code></a>
				,
				<a href="#minE"><code>minE</code></a>
				,
				<a href="#maxE"><code>maxE</code></a>
				,
				<a href="#toExpNeg"><code>toExpNeg</code></a>
				,
				<a href="#toExpPos"><code>toExpPos</code></a>
				,
				<a href="#modulo"><code>modulo</code></a>
				,
				<a href="#crypto"><code>crypto</code></a>
				。
			</p>
			<p>
				作为简单的对象属性，可以不使用
				<a href="#Dset"><code>set</code></a>
				来直接设置它们，但是这样会不检查配置值的有效性。例如：
			</p>
			<pre>
Decimal.set({ precision: 0 })
// '[DecimalError] Invalid argument: precision: 0'

Decimal.precision = 0
// 没有引发错误，这会导致计算结果不准确。</pre
			>

			<h5 id="precision">precision（精度）</h5>
			<p>
				<i>number</i>
				: integer, 包含
				<code>1</code>
				到
				<code>1e+9</code>
				<br />
				默认值:
				<code>20</code>
			</p>
			<p>
				运算结果的
				<i>最大有效位数</i>
				.
			</p>
			<p>
				所有返回Decimal的函数都会将返回值四舍五入为有效的精度
				<code>(precision)</code>
				。
				<a href="#decimal"><code>Decimal</code></a>
				,
				<a href="#abs"><code>absoluteValue</code></a>
				,
				<a href="#ceil"><code>ceil</code></a>
				,
				<a href="#floor"><code>floor</code></a>
				,
				<a href="#neg"><code>negated</code></a>
				,
				<a href="#round"><code>round</code></a>
				,
				<a href="#toDP"><code>toDecimalPlaces</code></a>
				,
				<a href="#toNearest"><code>toNearest</code></a>
				，
				<a href="#trunc"><code>truncated</code></a>
				除外。
			</p>
			<p>
				有关三角函数的精度请查看
				<code><a href="#Pi">Pi</a></code>
				.
			</p>
			<pre>
Decimal.set({ precision: 5 })
Decimal.precision                  // 5</pre
			>

			<h5 id="rounding">rounding（舍入）</h5>
			<p>
				<i>number</i>
				: integer, 包含
				<code>0</code>
				到
				<code>8</code>
				<br />
				默认值:
				<code>4</code>
				<a href="#modes">
					(
					<code>ROUND_HALF_UP 向上靠近最近值</code>
					)
				</a>
			</p>
			<p>
				<a href="#round"><code>round</code></a>
				,
				<a href="#toBinary"><code>toBinary</code></a>
				,
				<a href="#toDP"><code>toDecimalPlaces</code></a>
				,
				<a href="#toExponential"><code>toExponential</code></a>
				,
				<a href="#toFixed"><code>toFixed</code></a>
				,
				<a href="#toHexadecimal"><code>toHexadecimal</code></a>
				,
				<a href="#toNearest"><code>toNearest</code></a>
				,
				<a href="#toOctal"><code>toOctal</code></a>
				,
				<a href="#toPrecision"><code>toPrecision</code></a>
				,
				<a href="#toSD"><code>toSignificantDigits</code></a>
				的运算结果使用默认的舍入方式舍入到有效精度
				<code><a href="#precision">precision</a></code>
				。
			</p>
			<p>
				舍入模式
				<a href="#modes">rounding modes</a>
				可使用构造函数来进行设置。
			</p>
			<pre>
Decimal.set({ rounding: Decimal.ROUND_UP })
Decimal.set({ rounding: 0 })       // 0 相当于 Decimal.ROUND_UP
Decimal.rounding                   // 0</pre
			>

			<h5 id="minE">minE（最小指数）</h5>
			<p>
				<i>number</i>
				: integer, 包括
				<code>-9e15</code>
				到
				<code>0</code>
				<br />
				默认值:
				<code>-9e15</code>
			</p>
			<p>负指数值极限，低于该值会发生下溢至零。</p>
			<p>
				如果
				<code>Decimal</code>
				要由计算返回的十进制的指数小于
				<code>minE</code>
				，则该十进制的值将变为零。
			</p>

			<p>
				JavaScript 对于
				<code>-324</code>
				以下的指数会下溢至零.
			</p>
			<pre>
Decimal.set({ minE: -500 })
Decimal.minE                       // -500
new Decimal('1e-500')              // '1e-500'
new Decimal('9.9e-501')            // '0'

Decimal.set({ minE: -3 })
new Decimal(0.001)                 // '0.01'       e 为 -3
new Decimal(0.0001)                // '0'          e 为 -4，下溢至零</pre
			>

			<p>
				Decimal 非零小数的的最小值为：
				<code>1e-9000000000000000</code>
			</p>

			<h5 id="maxE">maxE</h5>
			<p>
				<i>number</i>
				: integer, 包括
				<code>0</code>
				到
				<code>9e15</code>
				<br />
				默认值:
				<code>9e15</code>
			</p>
			<p>
				正指数值极限，超过该极限值会发生
				<code>Infinity</code>
				溢出。
			</p>
			<p>
				<code>Decimal</code>
				如果要由计算返回的十进制的指数大于
				<code>maxE</code>
				，则该十进制的值将变为
				<code>Infinity</code>
				。
			</p>

			<p>
				JavaScript 对于
				<code>308</code>
				以上的指数会溢出至
				<code>Infinity</code>
				无穷大.
			</p>
			<pre>
Decimal.set({ maxE: 500 })
Decimal.maxE                       // 500
new Decimal('9.999e500')           // '9.999e+500'
new Decimal('1e501')               // 'Infinity'

Decimal.set({ maxE: 4 })
new Decimal(99999)                 // '99999'      e 为 4
new Decimal(100000)                // 'Infinity'</pre
			>

			<p>
				Decimal 的有限小数的最大值为：
				<code>9.999...e+9000000000000000</code>
			</p>

			<h5 id="toExpNeg">toExpNeg（负指数值）</h5>
			<p>
				<i>number</i>
				: integer, 包括
				<code>-9e15</code>
				到
				<code>0</code>
				<br />
				默认值:
				<code>-7</code>
			</p>
			<p>
				在负指数值及其以下的，
				<a href="#toString"><code>toString</code></a>
				返回指数表示法。
			</p>
			<pre>
Decimal.set({ toExpNeg: -7 })
Decimal.toExpNeg                   // -7
new Decimal(0.00000123)            // '0.00000123'       e 为 -6
new Decimal(0.000000123)           // '1.23e-7'

// 始终返回指数表示法:
Decimal.set({ toExpNeg: 0 })</pre
			>

			<p>
				JavaScript 数字对
				<code>-7</code>
				及以下的负指数使用指数表示法。
			</p>
			<p>
				不管
				<code>toExpNeg</code>
				的值如何，
				<a href="#toFixed"><code>toFixed</code></a>
				方法将始终以常规表示法返回值，
				<a href="#toExponential"><code>toExponential</code></a>
				方法将始终以指数形式返回值。
			</p>

			<h5 id="toExpPos">toExpPos（正指数值）</h5>
			<p>
				<i>number</i>
				: integer, 包括
				<code>0</code>
				到
				<code>9e15</code>
				<br />
				默认值:
				<code>20</code>
			</p>
			<p>
				大于等于正指数值时
				<a href="#toString"><code>toString</code></a>
				返回指数表示法。
			</p>
			<pre>
Decimal.set({ toExpPos: 2 })
Decimal.toExpPos                   // 2
new Decimal(12.3)                  // '12.3'        e 为 1
new Decimal(123)                   // '1.23e+2'

// 始终返回指数表示法:
Decimal.set({ toExpPos: 0 })</pre
			>

			<p>
				JavaScript 数字对正指数值大于
				<code>20</code>
				的值始终使用指数表示。
			</p>
			<p>
				不管
				<code>toExpPos</code>
				的值如何，
				<a href="#toFixed"><code>toFixed</code></a>
				方法将始终以常规表示法返回值，
				<a href="#toExponential"><code>toExponential</code></a>
				方法将始终以指数形式返回值。
			</p>

			<h5 id="modulo">modulo（模态）</h5>
			<p>
				<i>number</i>
				: integer, 包括
				<code>0</code>
				到
				<code>9</code>
				<br />
				默认值:
				<code>1</code>
				(
				<code>ROUND_DOWN</code>
				)
			</p>
			<p>
				计算模时使用的模式:
				<code>a mod n</code>
				.
			</p>
			<p>
				根据选择的模态模式
				<code>modulo</code>
				舍入
				<a href="#rounding"><code>rounding</code></a>
				计算商
				<code>q = a / n</code>
			</p>
			<p>
				余数
				<code>r</code>
				的计算方式
				<code>r = a - n * q</code>
				.
			</p>
			<p>下表显示常用的取模/取余模式，但是使用其他模式可能不会提供有用的结果。</p>
			<table>
				<tr>
					<th>属性</th>
					<th>值</th>
					<th>描述</th>
				</tr>
				<tr>
					<td>ROUND_UP</td>
					<td class="centre">0</td>
					<td>如果被除数为负，则余数为正，否则为负。</td>
				</tr>
				<tr>
					<td>ROUND_DOWN</td>
					<td class="centre">1</td>
					<td>
						使用截断除法，并匹配 JavaScript 余数运算符
						<code>%</code>
						的行为，余数部分与被除数相同。
					</td>
				</tr>
				<tr>
					<td>ROUND_FLOOR</td>
					<td class="centre">3</td>
					<td>
						余数部分与除数的符号相同（这与Python的
						<code>%</code>
						运算符相同）。
					</td>
				</tr>
				<tr>
					<td>ROUND_HALF_EVEN</td>
					<td class="centre">6</td>
					<td>
						<i>IEEE 754</i>
						二进制浮点数算术标准
					</td>
				</tr>
				<tr>
					<td>EUCLID</td>
					<td class="centre">9</td>
					<td>
						余数永远是正数，欧几里德除法:
						<code>q = sign(x) * floor(a / abs(x))</code>
						.
					</td>
				</tr>
			</table>
			<p>rounding/modulo 模式可在Decimal构造函数处设置。</p>
			<pre>
Decimal.set({ modulo: Decimal.EUCLID })
Decimal.set({ modulo: 9 })         // 9 相当于 Decimal.EUCLID
Decimal.modulo                     // 9</pre
			>

			<h5 id="crypto">crypto（加密）</h5>
			<p>
				<i>boolean</i>
				:
				<code>true/false</code>
				<br />
				默认值:
				<code>false</code>
			</p>
			<p>确定该值是否使用加密安全的伪随机数生成。</p>
			<p>
				详情查看
				<a href="#Drandom"><code>random</code></a>
				.
			</p>
			<pre>
// Node.js
global.crypto = require('crypto')

Decimal.crypto                     // false
Decimal.set({ crypto: true })
Decimal.crypto                     // true</pre
			>

			<h6 id="modes">Rounding modes 舍入模式</h6>
			<p>舍入模式为 Decimal 构造函数的属性，库本身未在内部引用它们。</p>
			<p>舍入模式0到6（包括四舍五入）与Java的BigDecimal类相同。</p>
			<table>
				<tr>
					<th>属性</th>
					<th>值</th>
					<th>描述</th>
				</tr>
				<tr>
					<td><b>ROUND_UP</b></td>
					<td class="centre">0</td>
					<td>远离零</td>
				</tr>
				<tr>
					<td><b>ROUND_DOWN</b></td>
					<td class="centre">1</td>
					<td>靠近零</td>
				</tr>
				<tr>
					<td><b>ROUND_CEIL</b></td>
					<td class="centre">2</td>
					<td>靠近正Infinity</td>
				</tr>
				<tr>
					<td><b>ROUND_FLOOR</b></td>
					<td class="centre">3</td>
					<td>靠近负Infinity</td>
				</tr>
				<tr>
					<td><b>ROUND_HALF_UP</b></td>
					<td class="centre">4</td>
					<td>向上靠近最近值，如果等距就四舍五入。</td>
				</tr>
				<tr>
					<td><b>ROUND_HALF_DOWN</b></td>
					<td class="centre">5</td>
					<td>向下靠近最近值，如果等距就舍入为0。</td>
				</tr>
				<tr>
					<td><b>ROUND_HALF_EVEN</b></td>
					<td class="centre">6</td>
					<td>最近值四舍五入</td>
				</tr>
				<tr>
					<td><b>ROUND_HALF_CEIL</b></td>
					<td class="centre">7</td>
					<td>最近值靠近+Infinity</td>
				</tr>
				<tr>
					<td><b>ROUND_HALF_FLOOR</b></td>
					<td class="centre">8</td>
					<td>最近值靠近-Infinity</td>
				</tr>
				<tr>
					<td><b>EUCLID</b></td>
					<td class="centre">9</td>
					<td>
						不是舍入模式, 详情查看
						<a href="#modulo">modulo</a>
					</td>
				</tr>
			</table>
			<pre>
Decimal.set({ rounding: Decimal.ROUND_CEIL })
Decimal.set({ rounding: 2 })       // 2 相当于 Decimal.ROUND_CEIL
Decimal.rounding                   // 2</pre
			>

			<h3>INSTANCE 实例</h3>

			<h4 id="prototype-methods">Methods 方法</h4>
			<p>这些方法是从 Decimal 构造函数原型继承下来的。</p>
			<p>Decimal 实例不会被其他方法更改.</p>
			<p>返回的方法可被链式调用:</p>
			<pre>x = new Decimal(2).times('999.999999999999999').dividedBy(4).ceil()</pre>
			<p>方法在执行前不会舍入参数.</p>
			<p>
				<code>&plusmn;0</code>
				,
				<code>&plusmn;Infinity</code>
				和
				<code>NaN</code>
				的处理方式和 JavaScript 一致.
			</p>
			<p>有些方法有较短的别名（库内部都是调用较短的别名）。</p>

			<h5 id="abs">
				absoluteValue（绝对值）
				<code class="inset">
					.abs()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>返回一个数的绝对值，不要符号，只要大小，返回一个新的 Decimal 。</p>
			<p>
				<a href="#precision">
					<code>precision</code>
					精度
				</a>
				设置不会对其产生影响。
			</p>
			<pre>
x = new Decimal(-0.8)
y = x.absoluteValue()         // '0.8'
z = y.abs()                   // '0.8'</pre
			>

			<h5 id="ceil">
				ceil（上入整数）
				<code class="inset">
					.ceil()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				向
				<code>Infinity</code>
				舍入，返回一个新的 Decimal 。
			</p>
			<p>
				<a href="#precision">
					<code>precision</code>
					精度
				</a>
				设置不会对其产生影响。
			</p>
			<pre>
x = new Decimal(1.3)
x.ceil()                      // '2'
y = new Decimal(-1.8)
y.ceil()                      // '-1'</pre
			>

			<h5 id="cmp">
				comparedTo（对比）
				<code class="inset">
					.cmp(x)
					<i>&rArr; number</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<table>
				<tr>
					<th>返回值</th>
					<th>&nbsp;</th>
				</tr>
				<tr>
					<td class="centre"><code>1</code></td>
					<td>
						Decimal 的值大于
						<code>x</code>
					</td>
				</tr>
				<tr>
					<td class="centre"><code>-1</code></td>
					<td>
						Decimal 的值小于
						<code>x</code>
					</td>
				</tr>
				<tr>
					<td class="centre"><code>0</code></td>
					<td>
						Decimal 的值等于
						<code>x</code>
					</td>
				</tr>
				<tr>
					<td class="centre"><code>NaN</code></td>
					<td>
						Decimal 的值或者
						<code>x</code>
						等于
						<code>NaN</code>
					</td>
				</tr>
			</table>
			<pre>
x = new Decimal(Infinity)
y = new Decimal(5)
x.comparedTo(y)                // 1
x.comparedTo(x.minus(1))       // 0
y.cmp(NaN)                     // NaN</pre
			>

			<h5 id="cos">
				cosine（余弦值）
				<code class="inset">
					.cos()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				计算 Decimal 弧度的余弦值，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>
				取值范围: [
				<code>-Infinity, Infinity</code>
				]
				<br />
				值范围: [
				<code>-1, 1</code>
				]
			</p>
			<p>
				有关此方法的精度限制，请查看
				<a href="#Pi"><code>Pi</code></a>
				.
			</p>
			<pre>
x = new Decimal(0.25)
x.cosine()                      // '0.96891242171064478414'
y = new Decimal(-0.25)
y.cos()                         // '0.96891242171064478414'</pre
			>

			<h5 id="cbrt">
				cubeRoot（立方根）
				<code class="inset">
					.cbrt()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				计算 Decimal 值的立方根（三次方根），按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>返回值会被正常的舍入，也就是说在舍入之前已经计算出无限个小数为。</p>
			<pre>
x = new Decimal(125)
x.cubeRoot()                    // '5'
y = new Decimal(3)
y.cbrt()                        // '1.4422495703074083823'</pre
			>

			<h5 id="dp">
				decimalPlaces（小数位数）
				<code class="inset">
					.dp()
					<i>&rArr; number</i>
				</code>
			</h5>
			<p>返回小数点后的位数，即小数点后的位数的值。</p>
			<pre>
x = new Decimal(1.234)
x.decimalPlaces()              // '3'
y = new Decimal(987.654321)
y.dp()                         // '6'</pre
			>

			<h5 id="div">
				dividedBy（除法）
				<code class="inset">
					.div(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				计算 Decimal 值的除以
				<code>x</code>
				，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<pre>
x = new Decimal(355)
y = new Decimal(113)
x.dividedBy(y)             // '3.14159292035398230088'
x.div(5)                   // '71'</pre
			>

			<h5 id="divToInt">
				dividedToIntegerBy（整数除法）
				<code class="inset">
					.divToInt(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				计算 Decimal 值的除以
				<code>x</code>
				，获取整数部分，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<pre>
x = new Decimal(5)
y = new Decimal(3)
x.dividedToIntegerBy(y)     // '1'
x.divToInt(0.7)             // '7'</pre
			>

			<h5 id="eq">
				equals（等于）
				<code class="inset">
					.eq(x)
					<i>&rArr; boolean</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				如果 Decimal 的值等于
				<code>x</code>
				的值，则返回
				<code>true</code>
				，否则返回
				<code>false</code>
				。 与JavaScript一样，
				<code>NaN</code>
				不等于
				<code>NaN</code>
				。
			</p>
			<p>
				备注: 此方法内部使用
				<code>cmp</code>
				实现.
			</p>
			<pre>
0 === 1e-324                     // true
x = new Decimal(0)
x.equals('1e-324')               // false
new Decimal(-0).eq(x)            // true  ( -0 === 0 )

y = new Decimal(NaN)
y.equals(NaN)                    // false</pre
			>

			<h5 id="floor">
				floor（向下取整）
				<code class="inset">
					.floor()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				向
				<code>负Infinity</code>
				舍入，保留整数。
			</p>
			<p>
				<a href="#precision">
					<code>precision</code>
					精度
				</a>
				设置不会对其产生影响。
			</p>
			<pre>
x = new Decimal(1.8)
x.floor()                   // '1'
y = new Decimal(-1.3)
y.floor()                   // '-2'</pre
			>

			<h5 id="gt">
				greaterThan（大于）
				<code class="inset">
					.gt(x)
					<i>&rArr; boolean</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				如果 Decimal 的值大于
				<code>x</code>
				的值，则返回
				<code>true</code>
				，否则返回
				<code>false</code>
				。
			</p>
			<p>
				备注: 此方法内部使用
				<code>cmp</code>
				实现.
			</p>
			<pre>
0.1 &gt; (0.3 - 0.2)                            // true
x = new Decimal(0.1)
x.greaterThan(Decimal(0.3).minus(0.2))       // false
new Decimal(0).gt(x)                         // false</pre
			>

			<h5 id="gte">
				greaterThanOrEqualTo（大于等于）
				<code class="inset">
					.gte(x)
					<i>&rArr; boolean</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				如果 Decimal 的值大于或者等于
				<code>x</code>
				的值，则返回
				<code>true</code>
				，否则返回
				<code>false</code>
				。
			</p>
			<p>
				备注: 此方法内部使用
				<code>cmp</code>
				实现.
			</p>
			<pre>
(0.3 - 0.2) &gt;= 0.1                       // false
x = new Decimal(0.3).minus(0.2)
x.greaterThanOrEqualTo(0.1)              // true
new Decimal(1).gte(x)                    // true</pre
			>

			<h5 id="cosh">
				hyperbolicCosine（双曲余弦值）
				<code class="inset">
					.cosh()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				计算 Decimal 值的双曲余弦值，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>
				取值范围: [
				<code>-Infinity, Infinity</code>
				]
				<br />
				值范围: [
				<code>1, Infinity</code>
				]
			</p>
			<p>
				有关此方法的精度限制，请查看
				<a href="#Pi"><code>Pi</code></a>
				.
			</p>
			<pre>
x = new Decimal(1)
x.hyperbolicCosine()                     // '1.5430806348152437785'
y = new Decimal(0.5)
y.cosh()                                 // '1.1276259652063807852'</pre
			>

			<h5 id="sinh">
				hyperbolicSine（双曲正弦值）
				<code class="inset">
					.sinh()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				计算 Decimal 值的双曲正弦值，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>
				取值范围: [
				<code>-Infinity, Infinity</code>
				]
				<br />
				值范围: [
				<code>-Infinity, Infinity</code>
				]
			</p>
			<p>
				有关此方法的精度限制，请查看
				<a href="#Pi"><code>Pi</code></a>
				.
			</p>
			<pre>
x = new Decimal(1)
x.hyperbolicSine()                       // '1.1752011936438014569'
y = new Decimal(0.5)
y.sinh()                                 // '0.52109530549374736162'</pre
			>

			<h5 id="tanh">
				hyperbolicTangent（双曲正切值）
				<code class="inset">
					.tanh()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				计算 Decimal 值的双曲正切值，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>
				取值范围: [
				<code>-Infinity, Infinity</code>
				]
				<br />
				值范围: [
				<code>-1, 1</code>
				]
			</p>
			<p>
				有关此方法的精度限制，请查看
				<a href="#Pi"><code>Pi</code></a>
				.
			</p>
			<pre>
x = new Decimal(1)
x.hyperbolicTangent()                    // '0.76159415595576488812'
y = new Decimal(0.5)
y.tanh()                                 // '0.4621171572600097585'</pre
			>

			<h5 id="acos">
				inverseCosine（反余弦值）
				<code class="inset">
					.acos()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				计算 Decimal 值的反余弦值，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>
				取值范围: [
				<code>-1, 1</code>
				]
				<br />
				值范围: [
				<code>0, pi</code>
				]
			</p>
			<p>
				有关此方法的精度限制，请查看
				<a href="#Pi"><code>Pi</code></a>
				.
			</p>
			<pre>
x = new Decimal(0)
x.inverseCosine()                        // '1.5707963267948966192'
y = new Decimal(0.5)
y.acos()                                 // '1.0471975511965977462'</pre
			>

			<h5 id="acosh">
				inverseHyperbolicCosine（反双曲余弦值）
				<code class="inset">
					.acosh()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				计算 Decimal 值的反双曲余弦值，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>
				取值范围: [
				<code>1, Infinity</code>
				]
				<br />
				值范围: [
				<code>0, Infinity</code>
				]
			</p>
			<p>
				有关此方法的精度限制，请查看
				<a href="#Pi"><code>Pi</code></a>
				.
			</p>
			<pre>
x = new Decimal(5)
x.inverseHyperbolicCosine()              // '2.2924316695611776878'
y = new Decimal(50)
y.acosh()                                // '4.6050701709847571595'</pre
			>

			<h5 id="asinh">
				inverseHyperbolicSine（反双曲正弦值）
				<code class="inset">
					.asinh()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				计算 Decimal 值的反双曲正弦值，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>
				取值范围: [
				<code>-Infinity, Infinity</code>
				]
				<br />
				值范围: [
				<code>-Infinity, Infinity</code>
				]
			</p>
			<p>
				有关此方法的精度限制，请查看
				<a href="#Pi"><code>Pi</code></a>
				.
			</p>
			<pre>
x = new Decimal(5)
x.inverseHyperbolicSine()                // '2.3124383412727526203'
y = new Decimal(50)
y.asinh()                                // '4.6052701709914238266'</pre
			>

			<h5 id="atanh">
				inverseHyperbolicTangent（反双曲正切值）
				<code class="inset">
					.atanh()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				计算 Decimal 值的反双曲正切值，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>
				取值范围: [
				<code>-1, 1</code>
				]
				<br />
				值范围: [
				<code>-Infinity, Infinity</code>
				]
			</p>
			<p>
				有关此方法的精度限制，请查看
				<a href="#Pi"><code>Pi</code></a>
				.
			</p>
			<pre>
x = new Decimal(0.5)
x.inverseHyperbolicTangent()             // '0.5493061443340548457'
y = new Decimal(0.75)
y.atanh()                                // '0.97295507452765665255'</pre
			>

			<h5 id="asin">
				inverseSine（反正弦值）
				<code class="inset">
					.asin()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				计算 Decimal 值的反正弦值，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>
				取值范围: [
				<code>-1, 1</code>
				]
				<br />
				值范围: [
				<code>-pi/2, pi/2</code>
				]
			</p>
			<p>
				有关此方法的精度限制，请查看
				<a href="#Pi"><code>Pi</code></a>
				.
			</p>
			<pre>
x = new Decimal(0.5)
x.inverseSine()                          // '0.52359877559829887308'
y = new Decimal(0.75)
y.asin()                                 // '0.84806207898148100805'</pre
			>

			<h5 id="atan">
				inverseTangent（反正切值）
				<code class="inset">
					.atan()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				计算 Decimal 值的反正切值，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>
				取值范围: [
				<code>-Infinity, Infinity</code>
				]
				<br />
				值范围: [
				<code>-pi/2, pi/2</code>
				]
			</p>
			<p>
				有关此方法的精度限制，请查看
				<a href="#Pi"><code>Pi</code></a>
				.
			</p>
			<pre>
x = new Decimal(0.5)
x.inverseTangent()                       // '0.46364760900080611621'
y = new Decimal(0.75)
y.atan()                                 // '0.6435011087932843868'</pre
			>

			<h5 id="isFinite">
				isFinite（判断有限数）
				<code class="inset">
					.isFinite()
					<i>&rArr; boolean</i>
				</code>
			</h5>
			<p>
				如果这个 Decimal 数是一个有限数，返回
				<code>true</code>
				，反之返回
				<code>false</code>
				.
				<br />
				一个 Decimal 数的非有限数有
				<code>NaN</code>
				,
				<code>Infinity</code>
				和
				<code>-Infinity</code>
				.
			</p>
			<pre>
x = new Decimal(1)
x.isFinite()                             // true
y = new Decimal(Infinity)
y.isFinite()                             // false</pre
			>

			<p>
				备注: 如果
				<code>n &lt;= Number.MAX_VALUE</code>
				，也可以使用本地方法
				<code>isFinite()</code>
				进行判断.
			</p>
			<h5 id="isInt">
				isInteger（判断整数）
				<code class="inset">
					.isInt()
					<i>&rArr; boolean</i>
				</code>
			</h5>
			<p>
				如果这个 Decimal 是一个整数返回
				<code>true</code>
				，反之返回
				<code>false</code>
				.
			</p>
			<pre>
x = new Decimal(1)
x.isInteger()                            // true
y = new Decimal(123.456)
y.isInt()                                // false</pre
			>

			<h5 id="isNaN">
				isNaN
				<code class="inset">
					.isNaN()
					<i>&rArr; boolean</i>
				</code>
			</h5>
			<p>
				如果这个 Decimal 是一个
				<code>NaN</code>
				则返回
				<code>true</code>
				，反之返回
				<code>false</code>
				.
			</p>
			<pre>
x = new Decimal(NaN)
x.isNaN()                                // true
y = new Decimal('Infinity')
y.isNaN()                                // false</pre
			>

			<p>
				备注: 也可以使用本地的
				<code>isNaN()</code>
				.
			</p>

			<h5 id="isNeg">
				isNegative（判断负数）
				<code class="inset">
					.isNeg()
					<i>&rArr; boolean</i>
				</code>
			</h5>
			<p>
				如果这个 Decimal 是一个负数则返回
				<code>true</code>
				，反之返回
				<code>false</code>
				.
			</p>
			<pre>
x = new Decimal(-0)
x.isNegative()                           // true
y = new Decimal(2)
y.isNeg                                  // false</pre
			>

			<p>
				备注: 如果
				<code>n &lt;= -Number.MIN_VALUE</code>
				使用的是
				<code>n &lt; 0</code>
				计算的，也就是说他们得到的结果是一样的.
			</p>

			<p>还要注意，符号零是按照IEEE浮点运算标准实现的 (IEEE 754).</p>

			<pre>
Decimal(0).valueOf()                 // '0'
Decimal(0).isNegative()              // false
Decimal(0).negated().valueOf()       // '-0'
Decimal(0).negated().isNegative()    // true
    </pre
			>

			<h5 id="isPos">
				isPositive（判断正数）
				<code class="inset">
					.isPos()
					<i>&rArr; boolean</i>
				</code>
			</h5>
			<p>
				如果这个 Decimal 是一个正数则返回
				<code>true</code>
				，反之返回
				<code>false</code>
				.
			</p>
			<pre>
x = new Decimal(0)
x.isPositive()                           // true
y = new Decimal(-2)
y.isPos                                  // false</pre
			>

			<p>
				备注: 如果
				<code>n &lt; 0</code>
				使用
				<code>n &lt;= -Number.MIN_VALUE</code>
				判断.
			</p>

			<h5 id="isZero">
				isZero（判断0）
				<code class="inset">
					.isZero()
					<i>&rArr; boolean</i>
				</code>
			</h5>
			<p>
				如果这个 Decimal 是一个 zero 则返回
				<code>true</code>
				，反之返回
				<code>false</code>
				.
			</p>
			<pre>
x = new Decimal(-0)
x.isZero() && x.isNeg()                  // true
y = new Decimal(Infinity)
y.isZero()                               // false</pre
			>

			<p>
				备注: 如果
				<code>n == 0</code>
				使用的是
				<code>n &gt;= Number.MIN_VALUE</code>
				.
			</p>

			<h5 id="lt">
				lessThan（小于）
				<code class="inset">
					.lt(x)
					<i>&rArr; boolean</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				如果这个 Decimal 小于
				<code>x</code>
				则返回
				<code>true</code>
				，反之返回
				<code>false</code>
				.
			</p>
			<p>
				备注: 此方法内部使用
				<code>cmp</code>
				.
			</p>
			<pre>
(0.3 - 0.2) &lt; 0.1                        // true
x = new Decimal(0.3).minus(0.2)
x.lessThan(0.1)                          // false
new Decimal(0).lt(x)                     // true</pre
			>

			<h5 id="lte">
				lessThanOrEqualTo（小于等于）
				<code class="inset">
					.lte(x)
					<i>&rArr; boolean</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				如果这个 Decimal 小于或者等于
				<code>x</code>
				则返回
				<code>true</code>
				，反之返回
				<code>false</code>
				.
			</p>
			<p>
				备注: 此方法内部使用
				<code>cmp</code>
				.
			</p>
			<pre>
0.1 &lt;= (0.3 - 0.2)                              // false
x = new Decimal(0.1)
x.lessThanOrEqualTo(Decimal(0.3).minus(0.2))    // true
new Decimal(-1).lte(x)                          // true</pre
			>

			<h5 id="log">
				logarithm（对数）
				<code class="inset">
					.log(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				计算 Decimal 值以
				<code>x</code>
				为底的对数，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>
				备注：如果
				<code>x</code>
				为10，可以省略不写.
			</p>
			<pre>
x = new Decimal(1000)
x.logarithm()                            // '3'
y = new Decimal(256)
y.log(2)                                 // '8'</pre
			>

			<p>
				如果进行了舍入，结果的误差为
				<code>1</code>
				<i>ulp</i>
				（最后一位的单位）.
			</p>
			<p>
				底数为
				<code>2</code>
				或者
				<code>10</code>
				结果是不会产生误差的.
			</p>
			<p>
				有关此方法可能返回错误舍入结果的情况，请参阅
				<a href="#pow"><code>toPower</code></a>
				，有关精度限制，请参阅
				<a href="#ln"><code>naturalLogarithm</code></a>
				。
			</p>
			<p>该方法的性能随着位数的增加呈指数级下降.</p>

			<h5 id="sub">
				minus（减法）
				<code class="inset">
					.minus(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				计算 Decimal 值减去
				<code>x</code>
				的结果，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<pre>
0.3 - 0.1                                // 0.19999999999999998
x = new Decimal(0.3)
x.minus(0.1)                             // '0.2'</pre
			>

			<h5 id="mod">
				modulo（取模）
				<code class="inset">
					.mod(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				计算 Decimal 值取
				<code>x</code>
				的模，按照设置的
				<a href="#rounding">
					<code>rounding</code>
					（舍入方式）
				</a>
				和
				<a href="#precision">
					<code>precision</code>
					（精度）
				</a>
				， 返回一个新的 Decimal 对象.
			</p>
			<p>
				The value returned, and in particular its sign, is dependent on the value of the
				<a href="#modulo"><code>modulo</code></a>
				property of this Decimal's constructor. If it is
				<code>1</code>
				(default value), the result will have the same sign as this Decimal, and it will match that of Javascript's
				<code>%</code>
				operator (within the limits of double precision) and BigDecimal's
				<code>remainder</code>
				method.
			</p>
			<p>
				See
				<a href="#modulo"><code>modulo</code></a>
				for a description of the other modulo modes.
			</p>
			<pre>
1 % 0.9                                  // 0.09999999999999998
x = new Decimal(1)
x.modulo(0.9)                            // '0.1'

y = new Decimal(8)
z = new Decimal(-3)
Decimal.modulo = 1
y.mod(z)                                 // '2'
Decimal.modulo = 3
y.mod(z)                                 // '-1'</pre
			>

			<h5 id="exp">
				naturalExponential
				<code class="inset">
					.exp()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				Returns a new Decimal whose value is the base
				<code>e</code>
				(Euler's number, the base of the natural logarithm) exponential of the value of this Decimal, rounded to
				<a href="#precision"><code>precision</code></a>
				significant digits using rounding mode
				<a href="#rounding"><code>rounding</code></a>
				.
			</p>
			<p>
				The
				<code><a href="#ln">naturalLogarithm</a></code>
				function is the inverse of this function.
			</p>
			<pre>
x = new Decimal(1)
x.naturalExponential()                   // '2.7182818284590452354'
y = new Decimal(2)
y.exp()                                  // '7.3890560989306502272'</pre
			>

			<p>
				The return value will be correctly rounded, i.e. rounded as if the result was first calculated to an infinite number of correct digits before rounding. (The
				mathematical result of the exponential function is non-terminating, unless its argument is
				<code>0</code>
				).
			</p>
			<p>The performance of this method degrades exponentially with increasing digits.</p>

			<h5 id="ln">
				naturalLogarithm
				<code class="inset">
					.ln()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				Returns a new Decimal whose value is the natural logarithm of the value of this Decimal, rounded to
				<a href="#precision"><code>precision</code></a>
				significant digits using rounding mode
				<a href="#rounding"><code>rounding</code></a>
				.
			</p>
			<p>
				The natural logarithm is the inverse of the
				<code><a href="#exp">naturalExponential</a></code>
				function.
			</p>
			<pre>
x = new Decimal(10)
x.naturalLogarithm()                     // '2.3026'
y = new Decimal('1.23e+30')
y.ln()                                   // '69.28'</pre
			>

			<p>
				The return value will be correctly rounded, i.e. rounded as if the result was first calculated to an infinite number of correct digits before rounding. (The
				mathematical result of the natural logarithm function is non-terminating, unless its argument is
				<code>1</code>
				).
			</p>
			<p>
				Internally, this method is dependent on a constant whose value is the natural logarithm of
				<code>10</code>
				. This
				<code>LN10</code>
				variable in the source code currently has a precision of
				<code>1025</code>
				digits, meaning that this method can accurately calculate up to
				<code>1000</code>
				digits.
			</p>
			<p>
				If more than
				<code>1000</code>
				digits is required then the precision of
				<code>LN10</code>
				will need to be increased to
				<code>25</code>
				digits more than is required - though, as the time-taken by this method increases exponentially with increasing digits, it is unlikely to be viable to calculate
				over
				<code>1000</code>
				digits anyway.
			</p>

			<h5 id="neg">
				negated
				<code class="inset">
					.neg()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				Returns a new Decimal whose value is the value of this Decimal negated, i.e. multiplied by
				<code>-1</code>
				.
			</p>
			<p>
				The return value is not affected by the value of the
				<a href="#precision"><code>precision</code></a>
				setting.
			</p>
			<pre>
x = new Decimal(1.8)
x.negated()                              // '-1.8'
y = new Decimal(-1.3)
y.neg()                                  // '1.3'</pre
			>

			<h5 id="add">
				plus
				<code class="inset">
					.plus(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				Returns a new Decimal whose value is the value of this Decimal plus
				<code>x</code>
				, rounded to
				<a href="#precision"><code>precision</code></a>
				significant digits using rounding mode
				<a href="#rounding"><code>rounding</code></a>
				.
			</p>
			<pre>
0.1 + 0.2                                // 0.30000000000000004
x = new Decimal(0.1)
y = x.plus(0.2)                          // '0.3'
new Decimal(0.7).plus(x).plus(y)         // '1.1'</pre
			>

			<h5 id="sd">
				precision
				<code class="inset">
					.sd([include_zeros])
					<i>&rArr; number</i>
				</code>
			</h5>
			<p>Returns the number of significant digits of the value of this Decimal.</p>
			<p>
				If
				<code>include_zeros</code>
				is
				<code>true</code>
				or
				<code>1</code>
				then any trailing zeros of the integer part of a number are counted as significant digits, otherwise they are not.
			</p>
			<pre>
x = new Decimal(1.234)
x.precision()                            // '4'
y = new Decimal(987000)
y.sd()                                   // '3'
y.sd(true)                               // '6'</pre
			>

			<h5 id="round">
				round
				<code class="inset">
					.round()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				Returns a new Decimal whose value is the value of this Decimal rounded to a whole number using rounding mode
				<a href="#rounding"><code>rounding</code></a>
				.
			</p>
			<p>
				To emulate
				<code>Math.round</code>
				, set
				<a href="#rounding"><code>rounding</code></a>
				to
				<code>7</code>
				, i.e.
				<a href="#modes"><code>ROUND_HALF_CEIL</code></a>
				.
			</p>
			<pre>
Decimal.set({ rounding: 4 })
x = 1234.5
x.round()                                // '1235'

Decimal.rounding = Decimal.ROUND_DOWN
x.round()                                // '1234'
x                                        // '1234.5'</pre
			>

			<h5 id="sin">
				sine
				<code class="inset">
					.sin()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				Returns a new Decimal whose value is the sine of the value in radians of this Decimal, rounded to
				<a href="#precision"><code>precision</code></a>
				significant digits using rounding mode
				<a href="#rounding"><code>rounding</code></a>
				.
			</p>
			<p>
				Domain: [
				<code>-Infinity, Infinity</code>
				]
				<br />
				Range: [
				<code>-1, 1</code>
				]
			</p>
			<p>
				See
				<a href="#Pi"><code>Pi</code></a>
				for the precision limit of this method.
			</p>
			<pre>
x = new Decimal(0.5)
x.sine()                                 // '0.47942553860420300027'
y = new Decimal(0.75)
y.sin()                                  // '0.68163876002333416673'</pre
			>

			<h5 id="sqrt">
				squareRoot
				<code class="inset">
					.sqrt()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				Returns a new Decimal whose value is the square root of this Decimal, rounded to
				<a href="#precision"><code>precision</code></a>
				significant digits using rounding mode
				<a href="#rounding"><code>rounding</code></a>
				.
			</p>
			<p>The return value will be correctly rounded, i.e. rounded as if the result was first calculated to an infinite number of correct digits before rounding.</p>
			<p>
				This method is much faster than using the
				<a href="#pow"><code>toPower</code></a>
				method with an exponent of
				<code>0.5</code>
				.
			</p>
			<pre>
x = new Decimal(16)
x.squareRoot()                           // '4'
y = new Decimal(3)
y.sqrt()                                 // '1.73205080756887729353'
y.sqrt().eq( y.pow(0.5) )                // true</pre
			>

			<h5 id="tan">
				tangent
				<code class="inset">
					.tan()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				Returns a new Decimal whose value is the tangent of the value in radians of this Decimal, rounded to
				<a href="#precision"><code>precision</code></a>
				significant digits using rounding mode
				<a href="#rounding"><code>rounding</code></a>
				.
			</p>
			<p>
				Domain: [
				<code>-Infinity, Infinity</code>
				]
				<br />
				Range: [
				<code>-Infinity, Infinity</code>
				]
			</p>
			<p>
				See
				<a href="#Pi"><code>Pi</code></a>
				for the precision limit of this method.
			</p>
			<pre>
x = new Decimal(0.5)
x.tangent()                              // '0.54630248984379051326'
y = new Decimal(0.75)
y.tan()                                  // '0.93159645994407246117'</pre
			>

			<h5 id="mul">
				times
				<code class="inset">
					.times(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
			</p>
			<p>
				Returns a new Decimal whose value is the value of this Decimal times
				<code>x</code>
				, rounded to
				<a href="#precision"><code>precision</code></a>
				significant digits using rounding mode
				<a href="#rounding"><code>rounding</code></a>
				.
			</p>
			<pre>
0.6 * 3                                  // 1.7999999999999998
x = new Decimal(0.6)
y = x.times(3)                           // '1.8'
new Decimal('7e+500').times(y)           // '1.26e+501'</pre
			>

			<h5 id="toBinary">
				toBinary
				<code class="inset">
					.toBinary([sd [, rm]])
					<i>&rArr; string</i>
				</code>
			</h5>
			<p>
				<code>sd</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>1e+9</code>
				inclusive
				<br />
				<code>rm</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>8</code>
				inclusive
			</p>
			<p>
				Returns a string representing the value of this Decimal in binary, rounded to
				<code>sd</code>
				significant digits using rounding mode
				<code>rm</code>
				.
			</p>
			<p>
				If
				<code>sd</code>
				is defined, the return value will use binary exponential notation.
			</p>
			<p>
				If
				<code>sd</code>
				is omitted, the return value will be rounded to
				<a href="#precision"><code>precision</code></a>
				significant digits.
			</p>
			<p>
				If
				<code>rm</code>
				is omitted, rounding mode
				<a href="#rounding"><code>rounding</code></a>
				will be used.
			</p>
			<p>
				Throws on an invalid
				<code>sd</code>
				or
				<code>rm</code>
				value.
			</p>
			<pre>
x = new Decimal(256)
x.toBinary()                             // '0b100000000'
x.toBinary(1)                            // '0b1p+8'</pre
			>

			<h5 id="toDP">
				toDecimalPlaces
				<code class="inset">
					.toDP([dp [, rm]])
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>dp</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>1e+9</code>
				inclusive
				<br />
				<code>rm</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>8</code>
				inclusive.
			</p>
			<p>
				Returns a new Decimal whose value is the value of this Decimal rounded to
				<code>dp</code>
				decimal places using rounding mode
				<code>rm</code>
				.
			</p>
			<p>
				If
				<code>dp</code>
				is omitted, the return value will have the same value as this Decimal.
			</p>
			<p>
				If
				<code>rm</code>
				is omitted, rounding mode
				<a href="#rounding"><code>rounding</code></a>
				is used.
			</p>
			<p>
				Throws on an invalid
				<code>dp</code>
				or
				<code>rm</code>
				value.
			</p>
			<pre>
x = new Decimal(12.34567)
x.toDecimalPlaces(0)                      // '12'
x.toDecimalPlaces(1, Decimal.ROUND_UP)    // '12.4'

y = new Decimal(9876.54321)
y.toDP(3)                           // '9876.543'
y.toDP(1, 0)                        // '9876.6'
y.toDP(1, Decimal.ROUND_DOWN)       // '9876.5'</pre
			>

			<h5 id="toExponential">
				toExponential
				<code class="inset">
					.toExponential([dp [, rm]])
					<i>&rArr; string</i>
				</code>
			</h5>
			<p>
				<code>dp</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>1e+9</code>
				inclusive
				<br />
				<code>rm</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>8</code>
				inclusive
			</p>
			<p>
				Returns a string representing the value of this Decimal in exponential notation rounded using rounding mode
				<code>rm</code>
				to
				<code>dp</code>
				decimal places, i.e with one digit before the decimal point and
				<code>dp</code>
				digits after it.
			</p>
			<p>
				If the value of this Decimal in exponential notation has fewer than
				<code>dp</code>
				fraction digits, the return value will be appended with zeros accordingly.
			</p>
			<p>
				If
				<code>dp</code>
				is omitted, the number of digits after the decimal point defaults to the minimum number of digits necessary to represent the value exactly.
			</p>
			<p>
				If
				<code>rm</code>
				is omitted, rounding mode
				<a href="#rounding"><code>rounding</code></a>
				is used.
			</p>
			<p>
				Throws on an invalid
				<code>dp</code>
				or
				<code>rm</code>
				value.
			</p>
			<pre>
x = 45.6
y = new Decimal(x)
x.toExponential()                        // '4.56e+1'
y.toExponential()                        // '4.56e+1'
x.toExponential(0)                       // '5e+1'
y.toExponential(0)                       // '5e+1'
x.toExponential(1)                       // '4.6e+1'
y.toExponential(1)                       // '4.6e+1'
y.toExponential(1, Decimal.ROUND_DOWN)   // '4.5e+1'
x.toExponential(3)                       // '4.560e+1'
y.toExponential(3)                       // '4.560e+1'</pre
			>

			<h5 id="toFixed">
				toFixed
				<code class="inset">
					.toFixed([dp [, rm]])
					<i>&rArr; string</i>
				</code>
			</h5>
			<p>
				<code>dp</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>1e+9</code>
				inclusive
				<br />
				<code>rm</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>8</code>
				inclusive
			</p>
			<p>
				Returns a string representing the value of this Decimal in normal (fixed-point) notation rounded to
				<code>dp</code>
				decimal places using rounding mode
				<code>rm</code>
				.
			</p>
			<p>
				If the value of this Decimal in normal notation has fewer than
				<code>dp</code>
				fraction digits, the return value will be appended with zeros accordingly.
			</p>
			<p>
				Unlike
				<code>Number.prototype.toFixed</code>
				, which returns exponential notation if a number is greater or equal to
				<code>
					10
					<sup>21</sup>
				</code>
				, this method will always return normal notation.
			</p>
			<p>
				If
				<code>dp</code>
				is omitted, the return value will be unrounded and in normal notation. This is unlike
				<code>Number.prototype.toFixed</code>
				, which returns the value to zero decimal places, but is useful when because of the current
				<a href="#toExpNeg"><code>toExpNeg</code></a>
				or
				<a href="#toExpPos"><code>toExpNeg</code></a>
				values,
				<code><a href="#toString">toString</a></code>
				returns exponential notation.
			</p>
			<p>
				If
				<code>rm</code>
				is omitted, rounding mode
				<a href="#rounding"><code>rounding</code></a>
				is used.
			</p>
			<p>
				Throws on an invalid
				<code>dp</code>
				or
				<code>rm</code>
				value.
			</p>
			<pre>
x = 3.456
y = new Decimal(x)
x.toFixed()                       // '3'
y.toFixed()                       // '3.456'
y.toFixed(0)                      // '3'
x.toFixed(2)                      // '3.46'
y.toFixed(2)                      // '3.46'
y.toFixed(2, Decimal.ROUND_DOWN)  // '3.45'
x.toFixed(5)                      // '3.45600'
y.toFixed(5)                      // '3.45600'</pre
			>

			<h5 id="toFraction">
				toFraction
				<code class="inset">
					.toFraction([max_denominator])
					<i>&rArr; [Decimal, Decimal]</i>
				</code>
			</h5>
			<p>
				<code>max_denominator</code>
				:
				<i>number|string|Decimal</i>
				:
				<code>1</code>
				&gt;= integer &lt;
				<code>Infinity</code>
			</p>
			<p>
				Returns an array of two Decimals representing the value of this Decimal as a simple fraction with an integer numerator and an integer denominator. The denominator
				will be a positive non-zero value less than or equal to
				<code>max_denominator</code>
				.
			</p>
			<p>If a maximum denominator is omitted, the denominator will be the lowest value necessary to represent the number exactly.</p>
			<p>
				Throws on an invalid
				<code>max_denominator</code>
				value.
			</p>
			<pre>
x = new Decimal(1.75)
x.toFraction()                       // '7, 4'

pi = new Decimal('3.14159265358')
pi.toFraction()                      // '157079632679,50000000000'
pi.toFraction(100000)                // '312689, 99532'
pi.toFraction(10000)                 // '355, 113'
pi.toFraction(100)                   // '311, 99'
pi.toFraction(10)                    // '22, 7'
pi.toFraction(1)                     // '3, 1'</pre
			>
			>

			<h5 id="toHex">
				toHexadecimal
				<code class="inset">
					.toHex([sd [, rm]])
					<i>&rArr; string</i>
				</code>
			</h5>
			<p>
				<code>sd</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>1e+9</code>
				inclusive
				<br />
				<code>rm</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>8</code>
				inclusive
			</p>
			<p>
				Returns a string representing the value of this Decimal in hexadecimal, rounded to
				<code>sd</code>
				significant digits using rounding mode
				<code>rm</code>
				.
			</p>
			<p>
				If
				<code>sd</code>
				is defined, the return value will use binary exponential notation.
			</p>
			<p>
				If
				<code>sd</code>
				is omitted, the return value will be rounded to
				<a href="#precision"><code>precision</code></a>
				significant digits.
			</p>
			<p>
				If
				<code>rm</code>
				is omitted, rounding mode
				<a href="#rounding"><code>rounding</code></a>
				will be used.
			</p>
			<p>
				Throws on an invalid
				<code>sd</code>
				or
				<code>rm</code>
				value.
			</p>
			<pre>
x = new Decimal(256)
x.toHexadecimal()                        // '0x100'
x.toHex(1)                               // '0x1p+8'</pre
			>
			>

			<h5 id="toJSON">
				toJSON
				<code class="inset">
					.toJSON()
					<i>&rArr; string</i>
				</code>
			</h5>
			<p>
				As
				<a href="#valueOf"><code>valueOf</code></a>
				.
			</p>

			<h5 id="toNearest">
				toNearest
				<code class="inset">
					.toNearest(x [, rm])
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
				<br />
				<code>rm</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>8</code>
				inclusive
			</p>
			<p>
				Returns a new Decimal whose value is the nearest multiple of
				<code>x</code>
				in the direction of rounding mode
				<code>rm</code>
				, or
				<a href="#rounding"><code>rounding</code></a>
				if
				<code>rm</code>
				is omitted, to the value of this Decimal.
			</p>
			<p>
				The return value will always have the same sign as this Decimal, unless either this Decimal or
				<code>x</code>
				is
				<code>NaN</code>
				, in which case the return value will be also be
				<code>NaN</code>
				.
			</p>
			<p>
				The return value is not affected by the value of the
				<a href="#precision"><code>precision</code></a>
				setting.
			</p>
			<pre>
x = new Decimal(1.39)
x.toNearest(0.25)                        // '1.5'

y = new Decimal(9.499)
y.toNearest(0.5, Decimal.ROUND_UP)       // '9.5'
y.toNearest(0.5, Decimal.ROUND_DOWN)     // '9'</pre
			>
			>

			<h5 id="toNumber">
				toNumber
				<code class="inset">
					.toNumber()
					<i>&rArr; number</i>
				</code>
			</h5>
			<p>Returns the value of this Decimal converted to a primitive number.</p>
			<p>Type coercion with, for example, JavaScript's unary plus operator will also work, except that a Decimal with the value minus zero will convert to positive zero.</p>
			<pre>
x = new Decimal(456.789)
x.toNumber()                   // 456.789
+x                             // 456.789

y = new Decimal('45987349857634085409857349856430985')
y.toNumber()                   // 4.598734985763409e+34

z = new Decimal(-0)
1 / +z                         // Infinity
1 / z.toNumber()               // -Infinity</pre
			>
			>

			<h5 id="toOctal">
				toOctal
				<code class="inset">
					.toOctal([sd [, rm]])
					<i>&rArr; string</i>
				</code>
			</h5>
			<p>
				<code>sd</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>1e+9</code>
				inclusive
				<br />
				<code>rm</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>8</code>
				inclusive
			</p>
			<p>
				Returns a string representing the value of this Decimal in octal, rounded to
				<code>sd</code>
				significant digits using rounding mode
				<code>rm</code>
				.
			</p>
			<p>
				If
				<code>sd</code>
				is defined, the return value will use binary exponential notation.
			</p>
			<p>
				If
				<code>sd</code>
				is omitted, the return value will be rounded to
				<a href="#precision"><code>precision</code></a>
				significant digits.
			</p>
			<p>
				If
				<code>rm</code>
				is omitted, rounding mode
				<a href="#rounding"><code>rounding</code></a>
				will be used.
			</p>
			<p>
				Throws on an invalid
				<code>sd</code>
				or
				<code>rm</code>
				value.
			</p>
			<pre>
x = new Decimal(256)
x.toOctal()                              // '0o400'
x.toOctal(1)                             // '0o1p+8'</pre
			>
			>

			<h5 id="pow">
				toPower
				<code class="inset">
					.pow(x)
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>x</code>
				:
				<i>number|string|Decimal</i>
				: integer or non-integer
			</p>
			<p>
				Returns a new Decimal whose value is the value of this Decimal raised to the power
				<code>x</code>
				, rounded to
				<a href="#precision"><code>precision</code></a>
				significant digits using rounding mode
				<a href="#rounding"><code>rounding</code></a>
				.
			</p>
			<p>
				The performance of this method degrades exponentially with increasing digits. For non-integer exponents in particular, the performance of this method may not be
				adequate.
			</p>
			<pre>
Math.pow(0.7, 2)               // 0.48999999999999994
x = new Decimal(0.7)
x.toPower(2)                   // '0.49'
new Decimal(3).pow(-2)         // '0.11111111111111111111'

new Decimal(1217652.23).pow('98765.489305603941')
// '4.8227010515242461181e+601039'</pre
			>
			>
			<p><i>Is the pow function guaranteed to be correctly rounded?</i></p>
			<p>
				The return value will
				<i>almost always</i>
				be correctly rounded, i.e. rounded as if the result was first calculated to an infinite number of correct digits before rounding. If a result is incorrectly rounded
				the maximum error will be
				<code>1</code>
				<i>ulp</i>
				(unit in the last place).
			</p>
			<p>For non-integer and larger exponents this method uses the formula</p>
			<blockquote>
				<code>
					x
					<sup>y</sup>
					= exp(y*ln(x))
				</code>
			</blockquote>
			<p>
				As the mathematical return values of the
				<a href="#exp"><code>exp</code></a>
				and
				<a href="#ln"><code>ln</code></a>
				functions are both non-terminating (excluding arguments of
				<code>0</code>
				or
				<code>1</code>
				), the values of the Decimals returned by the functions as implemented by this library will necessarily be rounded approximations, which means that there can be no
				guarantee of correct rounding when they are combined in the above formula.
			</p>
			<p>
				The return value may, depending on the rounding mode, be incorrectly rounded only if the first
				<code>15</code>
				rounding digits are
				<code>15</code>
				zeros (and there are non-zero digits following at some point), or
				<code>15</code>
				nines, or a
				<code>5</code>
				or
				<code>4</code>
				followed by
				<code>14</code>
				nines.
			</p>
			<p>
				Therefore, assuming the first
				<code>15</code>
				rounding digits are each equally likely to be any digit,
				<code>0-9</code>
				, the probability of an incorrectly rounded result is less than
				<code>1</code>
				in
				<code>250,000,000,000,000</code>
				.
			</p>
			<p>An example of incorrect rounding:</p>
			<pre>
Decimal.set({ precision: 20, rounding: 1 })
new Decimal(28).pow('6.166675020000903537297764507632802193308677149')
// 839756321.64088511</pre
			>
			>
			<p>As the exact mathematical result begins</p>
			<pre>839756321.6408851099999999999999999999999999998969466049426031167...</pre>
			<p>
				and the rounding mode is set to
				<code><a href="#modes">ROUND_DOWN</a></code>
				, the correct return value should be
			</p>
			<pre>839756321.64088510999</pre>

			<h5 id="toPrecision">
				toPrecision
				<code class="inset">
					.toPrecision([sd [, rm]])
					<i>&rArr; string</i>
				</code>
			</h5>
			<p>
				<code>sd</code>
				:
				<i>number</i>
				: integer,
				<code>1</code>
				to
				<code>1e+9</code>
				inclusive
				<br />
				<code>rm</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>8</code>
				inclusive
			</p>
			<p>
				Returns a string representing the value of this Decimal rounded to
				<code>sd</code>
				significant digits using rounding mode
				<code>rm</code>
				.
			</p>
			<p>
				If
				<code>sd</code>
				is less than the number of digits necessary to represent the integer part of the value in normal (fixed-point) notation, then exponential notation is used.
			</p>
			<p>
				If
				<code>sd</code>
				is omitted, the return value is the same as
				<code><a href="#toString">toString</a></code>
				.
			</p>
			<p>
				If
				<code>rm</code>
				is omitted, rounding mode
				<a href="#rounding"><code>rounding</code></a>
				is used.
			</p>
			<p>
				Throws on an invalid
				<code>sd</code>
				or
				<code>rm</code>
				value.
			</p>
			<pre>
x = 45.6
y = new Decimal(x)
x.toPrecision()                          // '45.6'
y.toPrecision()                          // '45.6'
x.toPrecision(1)                         // '5e+1'
y.toPrecision(1)                         // '5e+1'
y.toPrecision(2, Decimal.ROUND_UP)       // '46'
y.toPrecision(2, Decimal.ROUND_DOWN)     // '45'
x.toPrecision(5)                         // '45.600'
y.toPrecision(5)                         // '45.600'</pre
			>
			>

			<h5 id="toSD">
				toSignificantDigits
				<code class="inset">
					.toSD([sd [, rm]])
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>
				<code>sd</code>
				:
				<i>number</i>
				: integer,
				<code>1</code>
				to
				<code>1e+9</code>
				inclusive.
				<br />
				<code>rm</code>
				:
				<i>number</i>
				: integer,
				<code>0</code>
				to
				<code>8</code>
				inclusive.
			</p>
			<p>
				Returns a new Decimal whose value is the value of this Decimal rounded to
				<code>sd</code>
				significant digits using rounding mode
				<code>rm</code>
				.
			</p>
			<p>
				If
				<code>sd</code>
				is omitted, the return value will be rounded to
				<a href="#precision"><code>precision</code></a>
				significant digits.
			</p>
			<p>
				If
				<code>rm</code>
				is omitted, rounding mode
				<a href="#rounding"><code>rounding</code></a>
				will be used.
			</p>
			<p>
				Throws on an invalid
				<code>sd</code>
				or
				<code>rm</code>
				value.
			</p>
			<pre>
Decimal.set({ precision: 5, rounding: 4 })
x = new Decimal(9876.54321)

x.toSignificantDigits()                          // '9876.5'
x.toSignificantDigits(6)                         // '9876.54'
x.toSignificantDigits(6, Decimal.ROUND_UP)       // '9876.55'
x.toSD(2)                                        // '9900'
x.toSD(2, 1)                                     // '9800'
x                                                // '9876.54321'</pre
			>
			>

			<h5 id="toString">
				toString
				<code class="inset">
					.toString()
					<i>&rArr; string</i>
				</code>
			</h5>
			<p>Returns a string representing the value of this Decimal.</p>
			<p>
				If this Decimal has a positive exponent that is equal to or greater than
				<a href="#toExpPos"><code>toExpPos</code></a>
				, or a negative exponent equal to or less than
				<a href="#toExpPos"><code>toExpNeg</code></a>
				, then exponential notation will be returned.
			</p>
			<pre>
x = new Decimal(750000)
x.toString()                             // '750000'
Decimal.set({ toExpPos: 5 })
x.toString()                             // '7.5e+5'

Decimal.set({ precision: 4 })
y = new Decimal('1.23456789')
y.toString()                             // '1.23456789'</pre
			>
			>

			<h5 id="trunc">
				truncated
				<code class="inset">
					.trunc()
					<i>&rArr; Decimal</i>
				</code>
			</h5>
			<p>Returns a new Decimal whose value is the value of this Decimal truncated to a whole number.</p>
			<p>
				The return value is not affected by the value of the
				<a href="#precision"><code>precision</code></a>
				setting.
			</p>
			<pre>
x = new Decimal(123.456)
x.truncated()                            // '123'
y = new Decimal(-12.3)
y.trunc()                                // '-12'</pre
			>
			>

			<h5 id="valueOf">
				valueOf
				<code class="inset">
					.valueOf()
					<i>&rArr; string</i>
				</code>
			</h5>
			<p>
				As
				<a href="#toString"><code>toString</code></a>
				, but zero is signed.
			</p>
			<pre>
x = new Decimal(-0)
x.valueOf()                              // '-0'</pre
			>
			>

			<h4 id="instance-properties">Properties</h4>
			<p>
				The value of a Decimal is stored in a normalised base
				<code>10000000</code>
				floating point format.
			</p>
			<p>A Decimal instance is an object with three properties:</p>
			<table>
				<tr>
					<th>Property</th>
					<th>Description</th>
					<th>Type</th>
					<th>Value</th>
				</tr>
				<tr>
					<td class="centre" id="digits"><b>d</b></td>
					<td>digits</td>
					<td>
						<i>number</i>
						<code style="color: #000">[]</code>
					</td>
					<td>
						Array of integers, each
						<code>0</code>
						-
						<code>1e7</code>
						, or
						<code>null</code>
					</td>
				</tr>
				<tr>
					<td class="centre" id="exponent"><b>e</b></td>
					<td>exponent</td>
					<td><i>number</i></td>
					<td>
						Integer,
						<code>-9e15</code>
						to
						<code>9e15</code>
						inclusive, or
						<code>NaN</code>
					</td>
				</tr>
				<tr>
					<td class="centre" id="sign"><b>s</b></td>
					<td>sign</td>
					<td><i>number</i></td>
					<td>
						<code>-1</code>
						,
						<code>1</code>
						, or
						<code>NaN</code>
					</td>
				</tr>
			</table>
			<p>All the properties are best considered to be read-only.</p>
			<p>As with JavaScript numbers, the original exponent and fractional trailing zeros of a value are not preserved.</p>
			<pre>
x = new Decimal(0.123)                   // '0.123'
x.toExponential()                        // '1.23e-1'
x.d                                      // [ 1230000 ]
x.e                                      // -1
x.s                                      // 1

y = new Number(-123.4567000e+2)          // '-12345.67'
y.toExponential()                        // '-1.234567e+4'
z = new Decimal('-123.4567000e+2')       // '-12345.67'
z.toExponential()                        // '-1.234567e+4'
z.d                                      // [ 12345, 6700000 ]
z.e                                      // 4
z.s                                      // -1</pre>

			<h4 id="zero-nan-infinity">Zero, NaN and Infinity</h4>
			<p>
				The table below shows how
				<code>&plusmn;0</code>
				,
				<code>NaN</code>
				and
				<code>&plusmn;Infinity</code>
				are stored.
			</p>
			<table>
				<tr>
					<th></th>
					<th>&plusmn;0</th>
					<th>NaN</th>
					<th>&plusmn;Infinity</th>
				</tr>
				<tr>
					<td><b>&nbsp;d&nbsp;</b></td>
					<td><code>[0]</code></td>
					<td><code>null</code></td>
					<td><code>null</code></td>
				</tr>
				<tr>
					<td><b>&nbsp;e&nbsp;</b></td>
					<td><code>0</code></td>
					<td><code>NaN</code></td>
					<td><code>NaN</code></td>
				</tr>
				<tr>
					<td><b>&nbsp;s&nbsp;</b></td>
					<td><code>&plusmn;1</code></td>
					<td><code>NaN</code></td>
					<td><code>&plusmn;1</code></td>
				</tr>
			</table>
			<pre>
x = new Number(-0)                       // 0
1 / x == -Infinity                       // true

y = new Decimal(-0)
y.d                                      // '0' ( [0].toString() )
y.e                                      //  0
y.s                                      // -1
y.toString()                             // '0'
y.valueOf()                              // '-0'</pre>

			<h4 id="Errors">Errors</h4>
			<p>
				The errors that are thrown are generic
				<code>Error</code>
				objects whose
				<code>message</code>
				property begins with
				<code>"[DecimalError]"</code>
				.
			</p>
			<p>To determine if an exception is a Decimal Error:</p>
			<pre>
try {
    // ...
} catch (e) {
    if ( e instanceof Error && /DecimalError/.test(e.message) ) {
        // ...
    }
}</pre>

			<h4 id="Pi">Pi</h4>
			<p>
				The maximum precision of the trigonometric methods is dependent on the internal value of the constant pi, which is defined as the string
				<code>PI</code>
				near the top of the source file.
			</p>
			<p>
				It has a precision of
				<code>1025</code>
				digits, meaning that the trigonometric methods can calculate up to just over
				<code>1000</code>
				digits, but the actual figure depends on the precision of the argument passed to them. To calculate the actual figure use:
			</p>
			<p><b>maximum_result_precision = 1000 - argument_precision</b></p>
			For example, the following both work fine:
			<pre>
Decimal.set({precision: 991}).tan(123456789)
Decimal.set({precision: 9}).tan(991_digit_number)</pre>
			<p>
				as, for each, the result precision plus the argument precision, i.e.
				<code>991 + 9</code>
				and
				<code>9 + 991</code>
				, is less than or equal to
				<code>1000</code>
				.
			</p>
			<p>
				If greater precision is required then the value of
				<code>PI</code>
				will need to be extended to about
				<code>25</code>
				digits more than the precision required. The time taken by the methods will then be the limiting factor.
			</p>
			<p>The value can also be shortened to reduce the size of the source file if such high precision is not required.</p>
			<p>To get the value of pi:</p>
			<pre>pi = Decimal.acos(-1)</pre>

			<h2 id="faq">FAQ</h2>
			<h6>Why are trailing fractional zeros removed from Decimals?</h6>
			<p>
				Some arbitrary-precision libraries retain trailing fractional zeros as they can indicate the precision of a value. This can be useful but the results of arithmetic
				operations can be misleading.
			</p>
			<pre>
x = new BigDecimal("1.0")
y = new BigDecimal("1.1000")
z = x.add(y)                      // 2.1000

x = new BigDecimal("1.20")
y = new BigDecimal("3.45000")
z = x.multiply(y)                 // 4.1400000</pre>
			<p>To specify the precision of a value is to specify that the value lies within a certain range.</p>
			<p>
				In the first example,
				<code>x</code>
				has a value of
				<code>1.0</code>
				. The trailing zero shows the precision of the value, implying that it is in the range
				<code>0.95</code>
				to
				<code>1.05</code>
				. Similarly, the precision indicated by the trailing zeros of
				<code>y</code>
				indicates that the value is in the range
				<code>1.09995</code>
				to
				<code>1.10005</code>
				.
			</p>
			<p>
				If we add the two lowest values in the ranges we have,
				<code>0.95 + 1.09995 = 2.04995</code>
				, and if we add the two highest values we have,
				<code>1.05 + 1.10005 = 2.15005</code>
				, so the range of the result of the addition implied by the precision of its operands is
				<code>2.04995</code>
				to
				<code>2.15005</code>
				.
			</p>
			<p>
				The result given by BigDecimal of
				<code>2.1000</code>
				however, indicates that the value is in the range
				<code>2.09995</code>
				to
				<code>2.10005</code>
				and therefore the precision implied by its trailing zeros may be misleading.
			</p>
			<p>
				In the second example, the true range is
				<code>4.122744</code>
				to
				<code>4.157256</code>
				yet the BigDecimal answer of
				<code>4.1400000</code>
				indicates a range of
				<code>4.13999995</code>
				to
				<code>4.14000005</code>
				. Again, the precision implied by the trailing zeros may be misleading.
			</p>
			<p>
				This library, like binary floating point and most calculators, does not retain trailing fractional zeros. Instead, the
				<code>toExponential</code>
				,
				<code>toFixed</code>
				and
				<code>toPrecision</code>
				methods enable trailing zeros to be added if and when required.
				<br />
			</p>
		</div>
	</body>
</html>
