import {request} from '@/apis/request'
// import {request} from '@/apis/devAxios'
export function getList(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-need-plan-v1/page/sector`,
		method: 'get',
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
export const getCounts = (params)=>{
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-need-plan-v1/status/count`,
		method: 'get',
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//新增接口
export function add(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-need-plan-v1/add/sector`,
		method: 'post',
		data,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//编辑接口
export function edit(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-need-plan-v1/update/sector`,
		method: 'put',
		data,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//查询单条详情
export const getDetail = params => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-need-plan-v1/detail`,
		method: 'get',
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//批量删除
export function deleteRows(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-need-plan-v1/logicDelete/batch/sector`,
		method: 'delete',
		params,
	})
}

//发起流程
export const startProcess = data => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-srm-need-plan-v1/startProcessByCode`,
		method: 'post',
		data,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
export const excelOfIdsUrl = `/hpcc-ipop/v1_0/module/ipop-srm-need-plan-v1/excel/exportIds`
export const excelOfFiltersUrl = `/hpcc-ipop/v1_0/module/ipop-srm-need-plan-v1/excel/export/sector`

export default {
	getList,
	getDetail,
	add,
	edit,
	deleteRows,
	getCounts,
	startProcess,
	excelOfIdsUrl,
	excelOfFiltersUrl,
}