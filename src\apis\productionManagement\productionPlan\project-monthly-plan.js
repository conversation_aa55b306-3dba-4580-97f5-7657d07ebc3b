// 项目月度计划
import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-plan-project-month-v1`

// 获取列表分页数据
export function getProjectPlanPage(params) {
	return request({
		url: `${baseUrl}/page`,
		method: 'get',
		params,
	})
}
export function getProjectPlanPageNew(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-plan-overview-v1/page`,
		method: 'get',
		params,
	})
}

// 导出excel
export function exportProcess(params) {
	return request({
		url: `${baseUrl}/excel/export`,
		method: 'post',
    params,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}
// 导出选择excel
export function exportProcessByIds(data) {
	return request({
		url: `${baseUrl}/excel/exportIds`,
		method: 'post',
		data,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}

// 修改
export function edit(data, headers) {
	return request({
		url: `${baseUrl}/update`,
		method: 'put',
		data,
		headers,
	})
}

// 获取项目月度计划详情
export function getPlanProjectMonthDetail(params) {
	return request({
		url: `${baseUrl}/getPlanProjectMonthDetail`,
		method: 'get',
		params,
	})
}
