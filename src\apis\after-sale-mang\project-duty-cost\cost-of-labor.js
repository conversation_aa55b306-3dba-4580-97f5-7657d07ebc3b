// 项目责任成本--人工费
import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-csm-dispatch-cost-stat-labor-v1`

/** 获取分页列表 */
export function getPageList(params) {
	return request({
		url: `${baseUrl}/page`,
		method: 'get',
		params,
	})
}
/** 获取不分页 */
export function getAllList(params) {
	return request({
		url: `${baseUrl}/list`,
		method: 'get',
		params,
	})
}
/** 保存草稿 */
export function add(data) {
	return request({
		url: `${baseUrl}/add`,
		method: 'post',
		data,
	})
}

/** 修改 */
export function edit(data) {
	return request({
		url: `${baseUrl}/update`,
		method: 'put',
		data,
	})
}
/** 发起流程 */
export function startProcess(data) {
	return request({
		url: `${baseUrl}/startProcessByCode`,
		method: 'post',
		data,
	})
}
/** 详情接口 */
export function getDetail(id) {
	return request({
		url: `${baseUrl}/detail`,
		method: 'get',
		params: {
			id,
		},
	})
}
/** 删除流程 */
export function deleteProcess(id) {
	return request({
		url: `${baseUrl}/delete`,
		method: 'delete',
		params: {
			id,
		},
	})
}
/** 批量删除流程 */
export function delBatchProcess(ids) {
	return request({
		url: `${baseUrl}/delete/batch`,
		method: 'delete',
		params: {
			ids,
		},
	})
}
/** 导出excel */
export function exportProcess(params) {
	return request({
		url: `${baseUrl}/excel/export`,
		method: 'post',
		params,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}
/** 导出选择excel */
export function exportProcessByIds(list) {
	return request({
		url: `${baseUrl}/excel/exportByIds`,
		method: 'post',
		data: list,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}
