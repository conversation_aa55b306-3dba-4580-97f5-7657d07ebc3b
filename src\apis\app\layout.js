import {request} from '@/apis/request'
import store from '@/store'

export function getAppLayoutConfig(params) {
	const {appId} = params
	return request({
		url: `/baseService/v1/app/configuration/layout/` + appId,
		method: 'get',
		params,
	})
}

// 应用或模板详情接口
export function queryApplicationMessage(params) {
	return request({
		params,
		url: `/baseService/v1/app/application/${params.appId}`,
		method: 'get',
		data: params,
	})
}