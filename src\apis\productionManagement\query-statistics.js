import {request} from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/plan-total-v1`

// **************************声场管理-查询统计**********************************
// 生产日报详情
export function dailyDetailApi(params) {
	return request({
		url: `${baseUrl}/planQuery/productDailyDetail`,
		method: 'get',
		params,
	})
}

// 生产日报生成
export function dailyGenerateApi(params) {
	return request({
		url: `${baseUrl}/planQuery/productDailyGenerate`,
		method: 'get',
		params,
	})
}

// 生产日报查询(不分页)
export function dailyQueryApi(data) {
	return request({
		url: `${baseUrl}/planQuery/productDailyPage`,
		method: 'POST',
		data,
	})
}

// 生产月报详情
export function moonDetailApi(params) {
	return request({
		url: `${baseUrl}/planQuery/productMonthDetail`,
		method: 'get',
		params,
	})
}

// 生产月报详情(日报详情)
export function monthInDayDetailApi(params) {
	return request({
		url: `${baseUrl}/planQuery/productMonthDetail/reportDayData`,
		method: 'get',
		params,
	})
}

// 生产月报统计
export function moonStatisticsApi(data) {
	return request({
		url: `${baseUrl}/planQuery/productMonthPage`,
		method: 'POST',
		data,
	})
}

// 生产产量分析
export function yieldQueryApi(data) {
	return request({
		url: `${baseUrl}/planQuery/productOutputAnalysis`,
		method: 'POST',
		data,
	})
}



// 生产情况一览表（风电-钢构）
export function overviewListApi(params) {
	return request({
		url: `${baseUrl}/planQuery/productSituationPage`,
		method: 'get',
		params,
	})
}

// 生产任务统计
export function taskStatisticsApi(data) {
	return request({
		url: `${baseUrl}/planQuery/productTaskAnalysis`,
		method: 'POST',
		data,
	})
}



// 生产日报查询导出Excel数据, 最多3000条
export const daily_export_url = `${baseUrl}/planQuery/productDaily/export`
// 生产月报统计导出Excel数据, 最多3000条
export const moon_export_url = `${baseUrl}/planQuery/productMonth/export`
// 生产情况一览表导出（风电-钢构）Excel数据, 最多3000条
export const overview_export_url = `${baseUrl}/planQuery/productSituation/export`
// 生产情况一览表导出选中（风电-钢构）Excel数据, 最多3000条
export const overview_export_select_url = `${baseUrl}/planQuery/productSituation/exportIds`
// 生产产量分析导出Excel数据, 最多3000条
export const yield_export_url = `${baseUrl}/planQuery/productOutputAnalysis/export`
// 生产任务统计导出Excel数据, 最多3000条
export const task_export_url = `${baseUrl}/planQuery/productTaskAnalysis/export`
// 生产月报详情(日报详情)导出Excel数据, 最多3000条
export const month_detail_export_url = `${baseUrl}/planQuery/productMonthDetail/reportDayDataExport`

