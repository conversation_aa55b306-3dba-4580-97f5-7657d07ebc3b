// 风电售后日报API
import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-aftsales-dayreport-info-v1`

/** 风电日报分页 */
export function getPageList(params) {
	return request({
		url: `${baseUrl}/page`,
		method: 'get',
		params,
	})
}
/** 钢结构日报分页 */
export function pageSteel(params) {
	return request({
		url: `${baseUrl}/pageSteel`,
		method: 'get',
		params,
	})
}
/** 机动消缺日报分页 */
export function pageAllocate(params) {
	return request({
		url: `${baseUrl}/pageAllocate`,
		method: 'get',
		params,
	})
}
/** 获取未生效数量 */
export function getPageCount(params) {
	return request({
		url: `${baseUrl}/pageCount`,
		method: 'get',
		params,
	})
}
/** 获取不分页 */
export function getAllList(params) {
	return request({
		url: `${baseUrl}/list`,
		method: 'get',
		params,
	})
}
/** 保存草稿 */
export function add(data) {
	return request({
		url: `${baseUrl}/add`,
		method: 'post',
		data,
	})
}

/** 生效未生效 */
export function updateStatus(params) {
	return request({
		url: `${baseUrl}/updateStatus`,
		method: 'put',
		params,
	})
}
/** 修改 */
export function edit(data) {
	return request({
		url: `${baseUrl}/update`,
		method: 'put',
		data,
	})
}
/** 发起流程 */
export function startProcess(data) {
	return request({
		url: `${baseUrl}/startProcessByCode`,
		method: 'post',
		data,
	})
}
/** 详情接口 */
export function getDetail(id) {
	return request({
		url: `${baseUrl}/detail`,
		method: 'get',
		params: {
			id,
		},
		isFormatParams: false,
		isFormatResponse: true,
	})
}
/** 删除流程 */
export function deleteProcess(id) {
	return request({
		url: `${baseUrl}/delete`,
		method: 'delete',
		params: {
			id,
		},
	})
}
/** 批量删除流程 */
export function delBatchProcess(ids) {
	return request({
		url: `${baseUrl}/delete/batch`,
		method: 'delete',
		params: {
			ids,
		},
	})
}
/** 导出excel */
export function exportProcess(params) {
	return request({
		url: `${baseUrl}/excel/export`,
		method: 'post',
		params,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}
/** 导出选择excel */
export function exportProcessByIds(list, params) {
	return request({
		url: `${baseUrl}/excel/exportByIds`,
		method: 'post',
		params,
		data: list,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}
export const exportAllUrl = `${baseUrl}/excel/export`
export const exportSelectUrl = `${baseUrl}/excel/exportByIds`
