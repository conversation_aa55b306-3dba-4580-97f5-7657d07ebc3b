import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-plan-wind-power-factory-v1`
const modelUrl = `/hpcc-ipop/v1_0/module/ipop-plan-wind-power-model-v1`

// ******************************风电机型-厂家api******************************

export function windManufactorAllListApi(params) {
	return request({
		url: `${baseUrl}/list`,
		method: 'get',
		params,
	})
}

//新建接口
export function windManufactorAddApi(data) {
	return request({
		url: `${baseUrl}/add`,
		method: 'post',
		data,
	})
}
//编辑接口
export function windManufactorEditApi(data) {
	return request({
		url: `${baseUrl}/update`,
		method: 'put',
		data,
	})
}
//查询单条详情
export const windManufactorGetDetailApi = params => {
	return request({
		url: `${baseUrl}/detail`,
		method: 'get',
		params,
	})
}
//批量删除
export function windManufactorDeleteRowsApi(params) {
	return request({
		url: `${baseUrl}/logicDelete/batch`,
		method: 'delete',
		params,
	})
}

// ******************************风电机型-型号api******************************

export function windPowerModelListApi(params) {
	return request({
		url: `${modelUrl}/page`,
		method: 'get',
		params,
	})
}
//新建接口
export function windPowerModelAddApi(data) {
	return request({
		url: `${modelUrl}/add`,
		method: 'post',
		data,
	})
}
//编辑接口
export function windPowerModelEditApi(data) {
	return request({
		url: `${modelUrl}/update`,
		method: 'put',
		data,
	})
}
//查询单条详情
export const windPowerModelGetDetailApi = params => {
	return request({
		url: `${modelUrl}/detail`,
		method: 'get',
		params,
	})
}
//批量删除
export function windPowerModelDeleteRowsApi(params) {
	return request({
		url: `${modelUrl}/logicDelete/batch`,
		method: 'delete',
		params,
	})
}

//批量删除
export function getWindPowerModelList(params) {
	return request({
		url: `${modelUrl}/getWindPowerModelList`,
		method: 'get',
		params,
	})
}
