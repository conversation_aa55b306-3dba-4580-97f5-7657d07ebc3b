import {request} from '@/apis/request'
// import {request} from '@/apis/devAxios'
export function getList(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-tech-drawing-info-v1/page`,
		method: 'get',
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
export const getCounts = (params)=>{
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-tech-drawing-info-v1/status/count`,
		method: 'get',
		params,
	})
}
export function getAllList(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-tech-drawing-info-v1/list`,
		method: 'get',
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//新建接口
export function add(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-tech-drawing-info-v1/add`,
		method: 'post',
		data,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//编辑接口
export function edit(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-tech-drawing-info-v1/update`,
		method: 'put',
		data,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//查询单条详情
export const getDetail = params => {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-tech-drawing-info-v1/detail`,
		method: 'get',
		params,
		isFormatParams:true,
		isFormatResponse:true,
	})
}
//批量删除
export function deleteRows(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/ipop-tech-drawing-info-v1/logicDelete/batch`,
		method: 'delete',
		params,
	})
}


// 导出所有，最多3000
export const excelOfFiltersUrl = `/hpcc-ipop/v1_0/module/ipop-tech-drawing-info-v1/excel/export`
// 导出选中
export const excelOfIdsUrl = `/hpcc-ipop/v1_0/module/ipop-tech-drawing-info-v1/excel/exportIds`

// 导入
export const uploadExcelUrl = `/hpcc-ipop/v1_0/module/ipop-tech-drawing-info-v1/excel/parse/import`
// 模板下载
export const templateUrl = `/hpcc-ipop/v1_0/module/file-info-v1/commDownloadTempFile?tempName=到图记录-基础信息-导入模板.xlsx`