// 年度生产计划
import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-plan-product-v1`

// 获取年度生产计划分页接口
export function getAnnualProductionPlanPage(params) {
	return request({
		url: `${baseUrl}/page`,
		method: 'get',
		params,
	})
}

// 获取年度生产计划详情接口
export function getAnnualProductionPlanDetails(params) {
	return request({
		url: `${baseUrl}/getYearPlanProductInfo`,
		method: 'get',
		params,
	})
}

// 获取月度生产计划详情接口
export function getMonthlyProductionPlanDetails(params) {
	return request({
		url: `${baseUrl}/getMonthPlanProductInfo`,
		method: 'get',
		params,
	})
}

// 重新生成月度生产计划的数据
export function reGenerateMonthlyProductionPlanData(params) {
	return request({
		url: `${baseUrl}/rebuildPlanProductInfo`,
		method: 'post',
		params,
	})
}

// 年度生产计划导出excel
export function yearExportProcess(params) {
	return request({
		url: `${baseUrl}/excel/yearExcelExport`,
		method: 'post',
    params,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}
// 年度生产计划导出选择excel
export function yearExportProcessByIds(data) {
	return request({
		url: `${baseUrl}/excel/yearExportIds`,
		method: 'post',
		data,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}

// 月度生产计划导出excel
export function monthExportProcess(params) {
	return request({
		url: `${baseUrl}/excel/export`,
		method: 'post',
    params,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}
// 月度生产计划导出选择excel
export function monthExportProcessByIds(data) {
	return request({
		url: `${baseUrl}/excel/exportIds`,
		method: 'post',
		data,
		showResponseHeaders: true,
		responseType: 'blob',
	})
}

