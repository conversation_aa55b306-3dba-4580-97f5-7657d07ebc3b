// 随车附件
import { request } from '@/apis/request'
// import { request } from '@/apis/devAxios'
const baseUrl = `/hpcc-ipop/v1_0/module/ipop-pmm-invoice-attachment-v1`

// 获取分页列表
export function getPageList(params) {
	return request({
		url: `${baseUrl}/page`,
		method: 'get',
		params,
	})
}

// 查询列表接口
export function getAllList(params) {
	return request({
		url: `${baseUrl}/list`,
		method: 'get',
		params,
	})
}

// 新增
export function add(data) {
	return request({
		url: `${baseUrl}/add`,
		method: 'post',
		data,
	})
}

// 修改
export function edit(data) {
	return request({
		url: `${baseUrl}/update`,
		method: 'put',
		data,
	})
}

// 详情接口
export function getDetail(params) {
	return request({
		url: `${baseUrl}/detail`,
		method: 'get',
		params
	})
}

// 逻辑删除接口, ids以逗号分隔多个id
export function deleteRows(params) {
	return request({
		url: `${baseUrl}/logicDelete/batch`,
		method: 'DELETE',
		params,
	})
}

/** 发起流程 */
export function startProcess(data) {
	return request({
		url: `${baseUrl}/startProcessByCode`,
		method: 'post',
		data,
	})
}

export const excelOfIdsUrl = `${baseUrl}/excel/exportIds`
export const excelOfFiltersUrl = `${baseUrl}/excel/export`
// // 导出excel
// export function exportProcess(params) {
// 	return request({
// 		url: `${baseUrl}/excel/export`,
// 		method: 'post',
//     params,
// 		showResponseHeaders: true,
// 		responseType: 'blob',
// 	})
// }
// // 导出选择excel
// export function exportProcessByIds(data) {
// 	return request({
// 		url: `${baseUrl}/excel/exportIds`,
// 		method: 'post',
// 		data,
// 		showResponseHeaders: true,
// 		responseType: 'blob',
// 	})
// }
