import {request} from '@/apis/request'
// import {request} from '@/apis/devAxios'
export function getList(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/dict-type-v1/page`,
		method: 'get',
		params,
	})
}
export function getAllList(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/dict-type-v1/list`,
		method: 'get',
		params,
	})
}
//新建接口
export function add(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/dict-type-v1/add`,
		method: 'post',
		data,
	})
}
//编辑接口
export function edit(data) {
	return request({
		url: `/hpcc-ipop/v1_0/module/dict-type-v1/update`,
		method: 'put',
		data,
	})
}
//查询单条详情
export const getDetail = params => {
	return request({
		url: `/hpcc-ipop/v1_0/module/dict-type-v1/detail`,
		method: 'get',
		params,
	})
}
//批量删除
export function deleteRows(params) {
	return request({
		url: `/hpcc-ipop/v1_0/module/dict-type-v1/logicDelete/batch`,
		method: 'delete',
		params,
	})
}
